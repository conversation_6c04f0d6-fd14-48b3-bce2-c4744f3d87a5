<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>仪表板 - 自动打包平台</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background: #f5f7fa;
      color: #333;
    }

    .header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 1rem 2rem;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }

    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      max-width: 1400px;
      margin: 0 auto;
    }

    .logo-section {
      display: flex;
      align-items: center;
      gap: 12px;
    }

    .logo {
      font-size: 32px;
    }

    .platform-info h1 {
      font-size: 24px;
      font-weight: 600;
    }

    .platform-info p {
      font-size: 14px;
      opacity: 0.9;
    }

    .user-section {
      display: flex;
      align-items: center;
      gap: 15px;
    }

    .user-info {
      text-align: right;
    }

    .user-name {
      font-weight: 600;
      font-size: 16px;
    }

    .user-role {
      font-size: 12px;
      opacity: 0.8;
    }

    .logout-btn {
      background: rgba(255,255,255,0.2);
      border: 1px solid rgba(255,255,255,0.3);
      color: white;
      padding: 8px 16px;
      border-radius: 6px;
      cursor: pointer;
      transition: all 0.3s ease;
    }

    .logout-btn:hover {
      background: rgba(255,255,255,0.3);
    }

    .container {
      max-width: 1400px;
      margin: 0 auto;
      padding: 2rem;
    }

    .welcome-section {
      background: white;
      border-radius: 12px;
      padding: 2rem;
      margin-bottom: 2rem;
      box-shadow: 0 2px 10px rgba(0,0,0,0.05);
      border: 1px solid #e1e5e9;
    }

    .welcome-title {
      font-size: 28px;
      font-weight: 600;
      margin-bottom: 8px;
      color: #333;
    }

    .welcome-subtitle {
      color: #666;
      font-size: 16px;
      margin-bottom: 1.5rem;
    }

    .quick-actions {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 1rem;
    }

    .action-card {
      background: linear-gradient(135deg, #667eea, #764ba2);
      color: white;
      padding: 1.5rem;
      border-radius: 10px;
      text-decoration: none;
      transition: all 0.3s ease;
      text-align: center;
    }

    .action-card:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
    }

    .action-icon {
      font-size: 32px;
      margin-bottom: 8px;
      display: block;
    }

    .action-title {
      font-size: 16px;
      font-weight: 600;
      margin-bottom: 4px;
    }

    .action-desc {
      font-size: 12px;
      opacity: 0.9;
    }

    .stats-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: 1.5rem;
      margin-bottom: 2rem;
    }

    .stat-card {
      background: white;
      border-radius: 12px;
      padding: 1.5rem;
      box-shadow: 0 2px 10px rgba(0,0,0,0.05);
      border: 1px solid #e1e5e9;
    }

    .stat-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 1rem;
    }

    .stat-title {
      font-size: 16px;
      font-weight: 600;
      color: #333;
    }

    .stat-icon {
      font-size: 24px;
      padding: 8px;
      border-radius: 8px;
      background: #f0f4ff;
    }

    .stat-number {
      font-size: 32px;
      font-weight: 700;
      color: #667eea;
      margin-bottom: 4px;
    }

    .stat-label {
      font-size: 14px;
      color: #666;
    }

    .recent-section {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 2rem;
    }

    .recent-card {
      background: white;
      border-radius: 12px;
      padding: 1.5rem;
      box-shadow: 0 2px 10px rgba(0,0,0,0.05);
      border: 1px solid #e1e5e9;
    }

    .recent-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 1rem;
      padding-bottom: 0.5rem;
      border-bottom: 1px solid #e1e5e9;
    }

    .recent-title {
      font-size: 18px;
      font-weight: 600;
      color: #333;
    }

    .view-all-link {
      color: #667eea;
      text-decoration: none;
      font-size: 14px;
      font-weight: 500;
    }

    .view-all-link:hover {
      color: #764ba2;
    }

    .recent-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0.75rem 0;
      border-bottom: 1px solid #f0f0f0;
    }

    .recent-item:last-child {
      border-bottom: none;
    }

    .item-info {
      flex: 1;
    }

    .item-name {
      font-weight: 500;
      color: #333;
      margin-bottom: 2px;
    }

    .item-meta {
      font-size: 12px;
      color: #666;
    }

    .item-status {
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 12px;
      font-weight: 500;
    }

    .status-success {
      background: #e8f5e8;
      color: #2e7d32;
    }

    .status-building {
      background: #fff3e0;
      color: #f57c00;
    }

    .status-failed {
      background: #ffebee;
      color: #c62828;
    }

    @media (max-width: 768px) {
      .container {
        padding: 1rem;
      }

      .recent-section {
        grid-template-columns: 1fr;
      }

      .header-content {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
      }

      .user-section {
        justify-content: center;
      }
    }
  </style>
</head>
<body>
  <header class="header">
    <div class="header-content">
      <div class="logo-section">
        <div class="logo">📦</div>
        <div class="platform-info">
          <h1>自动打包平台</h1>
          <p>企业级 ISO 镜像构建与管理系统</p>
        </div>
      </div>
      <div class="user-section">
        <div class="user-info">
          <div class="user-name" id="userName">管理员</div>
          <div class="user-role">系统管理员</div>
        </div>
        <button class="logout-btn" onclick="logout()">退出登录</button>
      </div>
    </div>
  </header>

  <div class="container">
    <div class="welcome-section">
      <h2 class="welcome-title">欢迎回来！</h2>
      <p class="welcome-subtitle">快速访问常用功能，管理您的项目和构建任务</p>

      <div class="quick-actions">
        <a href="project.html" class="action-card">
          <span class="action-icon">📋</span>
          <div class="action-title">项目管理</div>
          <div class="action-desc">查看和管理所有项目</div>
        </a>
        <a href="project_iso.html" class="action-card">
          <span class="action-icon">🚀</span>
          <div class="action-title">构建 ISO</div>
          <div class="action-desc">创建新的 ISO 镜像</div>
        </a>
        <a href="base_iso.html" class="action-card">
          <span class="action-icon">💿</span>
          <div class="action-title">基础镜像</div>
          <div class="action-desc">管理系统基础镜像</div>
        </a>
        <a href="build-monitor.html" class="action-card">
          <span class="action-icon">📊</span>
          <div class="action-title">构建监控</div>
          <div class="action-desc">实时监控构建状态</div>
        </a>
        <a href="user-management.html" class="action-card">
          <span class="action-icon">👥</span>
          <div class="action-title">用户管理</div>
          <div class="action-desc">管理系统用户</div>
        </a>
      </div>
    </div>

    <div class="stats-grid">
      <div class="stat-card">
        <div class="stat-header">
          <div class="stat-title">总项目数</div>
          <div class="stat-icon">📁</div>
        </div>
        <div class="stat-number">12</div>
        <div class="stat-label">个活跃项目</div>
      </div>

      <div class="stat-card">
        <div class="stat-header">
          <div class="stat-title">本月构建</div>
          <div class="stat-icon">🔨</div>
        </div>
        <div class="stat-number">48</div>
        <div class="stat-label">次成功构建</div>
      </div>

      <div class="stat-card">
        <div class="stat-header">
          <div class="stat-title">基础镜像</div>
          <div class="stat-icon">💿</div>
        </div>
        <div class="stat-number">8</div>
        <div class="stat-label">个可用镜像</div>
      </div>

      <div class="stat-card">
        <div class="stat-header">
          <div class="stat-title">成功率</div>
          <div class="stat-icon">✅</div>
        </div>
        <div class="stat-number">94%</div>
        <div class="stat-label">构建成功率</div>
      </div>
    </div>

    <div class="recent-section">
      <div class="recent-card">
        <div class="recent-header">
          <h3 class="recent-title">最近构建</h3>
          <a href="project_iso_history.html" class="view-all-link">查看全部</a>
        </div>

        <div class="recent-item">
          <div class="item-info">
            <div class="item-name">超融合3.2 - v3.2.1</div>
            <div class="item-meta">2分钟前 • ARM架构</div>
          </div>
          <div class="item-status status-building">构建中</div>
        </div>

        <div class="recent-item">
          <div class="item-info">
            <div class="item-name">云桌面2.0 - v2.0.23</div>
            <div class="item-meta">1小时前 • x86架构</div>
          </div>
          <div class="item-status status-success">成功</div>
        </div>

        <div class="recent-item">
          <div class="item-info">
            <div class="item-name">超融合3.2 - v3.2.0</div>
            <div class="item-meta">3小时前 • ARM架构</div>
          </div>
          <div class="item-status status-success">成功</div>
        </div>
      </div>

      <div class="recent-card">
        <div class="recent-header">
          <h3 class="recent-title">最近活动</h3>
          <a href="#" class="view-all-link">查看全部</a>
        </div>

        <div class="recent-item">
          <div class="item-info">
            <div class="item-name">添加了新的基础镜像</div>
            <div class="item-meta">alice • 30分钟前</div>
          </div>
        </div>

        <div class="recent-item">
          <div class="item-info">
            <div class="item-name">更新了项目模块配置</div>
            <div class="item-meta">bob • 2小时前</div>
          </div>
        </div>

        <div class="recent-item">
          <div class="item-info">
            <div class="item-name">创建了新项目</div>
            <div class="item-meta">charlie • 1天前</div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <script>
    // 检查登录状态
    function checkLoginStatus() {
      if (sessionStorage.getItem('isLoggedIn') !== 'true') {
        window.location.href = 'login.html';
        return;
      }

      // 显示用户名
      const currentUser = sessionStorage.getItem('currentUser');
      if (currentUser) {
        document.getElementById('userName').textContent = currentUser;
      }
    }

    // 退出登录
    function logout() {
      if (confirm('确定要退出登录吗？')) {
        sessionStorage.removeItem('isLoggedIn');
        sessionStorage.removeItem('currentUser');
        localStorage.removeItem('rememberLogin');
        localStorage.removeItem('username');
        window.location.href = 'login.html';
      }
    }

    // 模拟实时数据更新
    function updateStats() {
      // 这里可以添加实际的API调用来获取最新数据
      console.log('更新统计数据...');
    }

    // 页面加载完成后执行
    document.addEventListener('DOMContentLoaded', function() {
      checkLoginStatus();

      // 每30秒更新一次数据
      setInterval(updateStats, 30000);
    });
  </script>
</body>
</html>
