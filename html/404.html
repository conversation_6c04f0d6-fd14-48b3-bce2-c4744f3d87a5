<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>页面未找到 - 自动打包平台</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
      overflow: hidden;
    }
    
    /* 背景动画效果 */
    body::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
      animation: float 20s ease-in-out infinite;
    }
    
    @keyframes float {
      0%, 100% { transform: translateY(0px) rotate(0deg); }
      50% { transform: translateY(-20px) rotate(1deg); }
    }
    
    .error-container {
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(10px);
      border-radius: 20px;
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
      padding: 3rem;
      width: 100%;
      max-width: 600px;
      position: relative;
      z-index: 1;
      border: 1px solid rgba(255, 255, 255, 0.2);
      text-align: center;
    }
    
    .error-animation {
      margin-bottom: 2rem;
      position: relative;
    }
    
    .error-number {
      font-size: 120px;
      font-weight: 900;
      background: linear-gradient(135deg, #667eea, #764ba2);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
      line-height: 1;
      margin-bottom: 1rem;
      animation: bounce 2s ease-in-out infinite;
    }
    
    @keyframes bounce {
      0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
      }
      40% {
        transform: translateY(-10px);
      }
      60% {
        transform: translateY(-5px);
      }
    }
    
    .error-icon {
      font-size: 80px;
      margin-bottom: 1rem;
      animation: spin 3s linear infinite;
    }
    
    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
    
    .error-title {
      font-size: 32px;
      font-weight: 700;
      color: #333;
      margin-bottom: 1rem;
    }
    
    .error-subtitle {
      font-size: 18px;
      color: #666;
      margin-bottom: 2rem;
      line-height: 1.6;
    }
    
    .error-description {
      font-size: 16px;
      color: #777;
      margin-bottom: 2.5rem;
      line-height: 1.6;
    }
    
    .action-buttons {
      display: flex;
      gap: 1rem;
      justify-content: center;
      flex-wrap: wrap;
    }
    
    .btn {
      padding: 12px 24px;
      border: none;
      border-radius: 10px;
      font-size: 16px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      text-decoration: none;
      display: inline-flex;
      align-items: center;
      gap: 8px;
    }
    
    .btn-primary {
      background: linear-gradient(135deg, #667eea, #764ba2);
      color: white;
    }
    
    .btn-primary:hover {
      transform: translateY(-2px);
      box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
    }
    
    .btn-secondary {
      background: rgba(102, 126, 234, 0.1);
      color: #667eea;
      border: 2px solid rgba(102, 126, 234, 0.2);
    }
    
    .btn-secondary:hover {
      background: rgba(102, 126, 234, 0.2);
      transform: translateY(-2px);
    }
    
    .helpful-links {
      margin-top: 2.5rem;
      padding-top: 2rem;
      border-top: 1px solid #e1e5e9;
    }
    
    .helpful-links h3 {
      font-size: 18px;
      color: #333;
      margin-bottom: 1rem;
    }
    
    .links-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 1rem;
    }
    
    .link-card {
      background: #f8f9fa;
      padding: 1rem;
      border-radius: 8px;
      text-decoration: none;
      color: #333;
      transition: all 0.3s ease;
      border: 1px solid #e1e5e9;
    }
    
    .link-card:hover {
      background: #e9ecef;
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    }
    
    .link-icon {
      font-size: 24px;
      margin-bottom: 8px;
      display: block;
    }
    
    .link-title {
      font-weight: 600;
      margin-bottom: 4px;
    }
    
    .link-desc {
      font-size: 12px;
      color: #666;
    }
    
    .floating-shapes {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      overflow: hidden;
      z-index: 0;
    }
    
    .shape {
      position: absolute;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 50%;
      animation: float-shapes 15s infinite linear;
    }
    
    .shape:nth-child(1) {
      width: 80px;
      height: 80px;
      top: 20%;
      left: 10%;
      animation-delay: 0s;
    }
    
    .shape:nth-child(2) {
      width: 120px;
      height: 120px;
      top: 60%;
      right: 10%;
      animation-delay: 5s;
    }
    
    .shape:nth-child(3) {
      width: 60px;
      height: 60px;
      bottom: 20%;
      left: 20%;
      animation-delay: 10s;
    }
    
    .shape:nth-child(4) {
      width: 100px;
      height: 100px;
      top: 30%;
      right: 30%;
      animation-delay: 7s;
    }
    
    @keyframes float-shapes {
      0% { transform: translateY(0px) rotate(0deg); opacity: 0.7; }
      50% { transform: translateY(-100px) rotate(180deg); opacity: 0.3; }
      100% { transform: translateY(0px) rotate(360deg); opacity: 0.7; }
    }
    
    .search-suggestion {
      margin-top: 1.5rem;
      padding: 1rem;
      background: rgba(102, 126, 234, 0.05);
      border-radius: 8px;
      border: 1px solid rgba(102, 126, 234, 0.1);
    }
    
    .search-input {
      width: 100%;
      padding: 10px 16px;
      border: 2px solid #e1e5e9;
      border-radius: 8px;
      font-size: 14px;
      margin-top: 8px;
      transition: border-color 0.3s ease;
    }
    
    .search-input:focus {
      outline: none;
      border-color: #667eea;
      box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }
    
    @media (max-width: 768px) {
      .error-container {
        margin: 20px;
        padding: 2rem 1.5rem;
      }
      
      .error-number {
        font-size: 80px;
      }
      
      .error-title {
        font-size: 24px;
      }
      
      .error-subtitle {
        font-size: 16px;
      }
      
      .action-buttons {
        flex-direction: column;
        align-items: center;
      }
      
      .btn {
        width: 100%;
        max-width: 250px;
        justify-content: center;
      }
      
      .links-grid {
        grid-template-columns: 1fr;
      }
    }
  </style>
</head>
<body>
  <div class="floating-shapes">
    <div class="shape"></div>
    <div class="shape"></div>
    <div class="shape"></div>
    <div class="shape"></div>
  </div>
  
  <div class="error-container">
    <div class="error-animation">
      <div class="error-number">404</div>
      <div class="error-icon">🔍</div>
    </div>
    
    <h1 class="error-title">页面未找到</h1>
    <p class="error-subtitle">抱歉，您访问的页面不存在或已被移动</p>
    <p class="error-description">
      可能是链接地址错误，或者页面已被删除。请检查URL是否正确，或者尝试以下操作：
    </p>
    
    <div class="action-buttons">
      <a href="dashboard.html" class="btn btn-primary">
        <span>🏠</span>
        返回首页
      </a>
      <button class="btn btn-secondary" onclick="history.back()">
        <span>⬅️</span>
        返回上页
      </button>
    </div>
    
    <div class="search-suggestion">
      <h4>搜索您需要的内容：</h4>
      <input type="text" class="search-input" placeholder="输入关键词搜索..." onkeypress="handleSearch(event)">
    </div>
    
    <div class="helpful-links">
      <h3>常用功能</h3>
      <div class="links-grid">
        <a href="project.html" class="link-card">
          <span class="link-icon">📋</span>
          <div class="link-title">项目管理</div>
          <div class="link-desc">查看和管理所有项目</div>
        </a>
        
        <a href="base_iso.html" class="link-card">
          <span class="link-icon">💿</span>
          <div class="link-title">基础镜像</div>
          <div class="link-desc">管理系统基础镜像</div>
        </a>
        
        <a href="build-monitor.html" class="link-card">
          <span class="link-icon">📊</span>
          <div class="link-title">构建监控</div>
          <div class="link-desc">实时监控构建状态</div>
        </a>
        
        <a href="user-management.html" class="link-card">
          <span class="link-icon">👥</span>
          <div class="link-title">用户管理</div>
          <div class="link-desc">管理系统用户</div>
        </a>
      </div>
    </div>
  </div>
  
  <script>
    // 处理搜索
    function handleSearch(event) {
      if (event.key === 'Enter') {
        const searchTerm = event.target.value.trim();
        if (searchTerm) {
          // 模拟搜索功能
          alert(`搜索功能开发中，您搜索的是：${searchTerm}`);
        }
      }
    }
    
    // 页面加载动画
    document.addEventListener('DOMContentLoaded', function() {
      const container = document.querySelector('.error-container');
      container.style.opacity = '0';
      container.style.transform = 'translateY(20px)';
      
      setTimeout(() => {
        container.style.transition = 'all 0.6s ease';
        container.style.opacity = '1';
        container.style.transform = 'translateY(0)';
      }, 100);
    });
    
    // 添加一些交互效果
    document.querySelectorAll('.link-card').forEach(card => {
      card.addEventListener('mouseenter', function() {
        this.style.transform = 'translateY(-4px) scale(1.02)';
      });
      
      card.addEventListener('mouseleave', function() {
        this.style.transform = 'translateY(0) scale(1)';
      });
    });
  </script>
</body>
</html>
