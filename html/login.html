<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>登录 - 自动打包平台</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
      overflow: hidden;
    }
    
    /* 背景动画效果 */
    body::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
      animation: float 20s ease-in-out infinite;
    }
    
    @keyframes float {
      0%, 100% { transform: translateY(0px) rotate(0deg); }
      50% { transform: translateY(-20px) rotate(1deg); }
    }
    
    .login-container {
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(10px);
      border-radius: 20px;
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
      padding: 40px;
      width: 100%;
      max-width: 420px;
      position: relative;
      z-index: 1;
      border: 1px solid rgba(255, 255, 255, 0.2);
    }
    
    .logo-section {
      text-align: center;
      margin-bottom: 30px;
    }
    
    .logo {
      font-size: 48px;
      margin-bottom: 10px;
      background: linear-gradient(135deg, #667eea, #764ba2);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }
    
    .platform-title {
      font-size: 24px;
      font-weight: 600;
      color: #333;
      margin-bottom: 8px;
    }
    
    .platform-subtitle {
      font-size: 14px;
      color: #666;
      margin-bottom: 20px;
    }
    
    .form-group {
      margin-bottom: 20px;
      position: relative;
    }
    
    .form-group label {
      display: block;
      margin-bottom: 8px;
      font-weight: 500;
      color: #333;
      font-size: 14px;
    }
    
    .form-group input {
      width: 100%;
      padding: 12px 16px;
      border: 2px solid #e1e5e9;
      border-radius: 10px;
      font-size: 16px;
      transition: all 0.3s ease;
      background: #f8f9fa;
    }
    
    .form-group input:focus {
      outline: none;
      border-color: #667eea;
      background: white;
      box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }
    
    .form-group .input-icon {
      position: absolute;
      right: 12px;
      top: 38px;
      color: #999;
      font-size: 18px;
    }
    
    .remember-forgot {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 25px;
      font-size: 14px;
    }
    
    .remember-me {
      display: flex;
      align-items: center;
      gap: 8px;
    }
    
    .remember-me input[type="checkbox"] {
      width: auto;
      margin: 0;
    }
    
    .forgot-password {
      color: #667eea;
      text-decoration: none;
      transition: color 0.3s ease;
    }
    
    .forgot-password:hover {
      color: #764ba2;
    }
    
    .login-btn {
      width: 100%;
      padding: 14px;
      background: linear-gradient(135deg, #667eea, #764ba2);
      color: white;
      border: none;
      border-radius: 10px;
      font-size: 16px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;
    }
    
    .login-btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
    }
    
    .login-btn:active {
      transform: translateY(0);
    }
    
    .divider {
      text-align: center;
      margin: 25px 0;
      position: relative;
      color: #999;
      font-size: 14px;
    }
    
    .divider::before {
      content: '';
      position: absolute;
      top: 50%;
      left: 0;
      right: 0;
      height: 1px;
      background: #e1e5e9;
      z-index: 1;
    }
    
    .divider span {
      background: rgba(255, 255, 255, 0.95);
      padding: 0 15px;
      position: relative;
      z-index: 2;
    }
    
    .register-link {
      text-align: center;
      margin-top: 20px;
      font-size: 14px;
      color: #666;
    }
    
    .register-link a {
      color: #667eea;
      text-decoration: none;
      font-weight: 600;
      transition: color 0.3s ease;
    }
    
    .register-link a:hover {
      color: #764ba2;
    }
    
    .floating-shapes {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      overflow: hidden;
      z-index: 0;
    }
    
    .shape {
      position: absolute;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 50%;
      animation: float-shapes 15s infinite linear;
    }
    
    .shape:nth-child(1) {
      width: 80px;
      height: 80px;
      top: 20%;
      left: 10%;
      animation-delay: 0s;
    }
    
    .shape:nth-child(2) {
      width: 120px;
      height: 120px;
      top: 60%;
      right: 10%;
      animation-delay: 5s;
    }
    
    .shape:nth-child(3) {
      width: 60px;
      height: 60px;
      bottom: 20%;
      left: 20%;
      animation-delay: 10s;
    }
    
    @keyframes float-shapes {
      0% { transform: translateY(0px) rotate(0deg); opacity: 0.7; }
      50% { transform: translateY(-100px) rotate(180deg); opacity: 0.3; }
      100% { transform: translateY(0px) rotate(360deg); opacity: 0.7; }
    }
    
    .error-message {
      background: #fee;
      color: #c33;
      padding: 10px;
      border-radius: 8px;
      margin-bottom: 20px;
      font-size: 14px;
      border: 1px solid #fcc;
      display: none;
    }
    
    @media (max-width: 480px) {
      .login-container {
        margin: 20px;
        padding: 30px 25px;
      }
      
      .platform-title {
        font-size: 20px;
      }
      
      .logo {
        font-size: 40px;
      }
    }
  </style>
</head>
<body>
  <div class="floating-shapes">
    <div class="shape"></div>
    <div class="shape"></div>
    <div class="shape"></div>
  </div>
  
  <div class="login-container">
    <div class="logo-section">
      <div class="logo">📦</div>
      <h1 class="platform-title">自动打包平台</h1>
      <p class="platform-subtitle">企业级 ISO 镜像构建与管理系统</p>
    </div>
    
    <div class="error-message" id="errorMessage">
      用户名或密码错误，请重试
    </div>
    
    <form id="loginForm" onsubmit="handleLogin(event)">
      <div class="form-group">
        <label for="username">用户名</label>
        <input type="text" id="username" name="username" required placeholder="请输入用户名">
        <span class="input-icon">👤</span>
      </div>
      
      <div class="form-group">
        <label for="password">密码</label>
        <input type="password" id="password" name="password" required placeholder="请输入密码">
        <span class="input-icon">🔒</span>
      </div>
      
      <div class="remember-forgot">
        <label class="remember-me">
          <input type="checkbox" id="remember" name="remember">
          <span>记住我</span>
        </label>
        <a href="#" class="forgot-password" onclick="showForgotPassword()">忘记密码？</a>
      </div>
      
      <button type="submit" class="login-btn">
        登录
      </button>
    </form>
    
    <div class="divider">
      <span>或</span>
    </div>
    
    <div class="register-link">
      还没有账户？ <a href="register.html">立即注册</a>
    </div>
  </div>
  
  <script>
    function handleLogin(event) {
      event.preventDefault();
      
      const username = document.getElementById('username').value;
      const password = document.getElementById('password').value;
      const remember = document.getElementById('remember').checked;
      
      // 模拟登录验证
      if (username === 'admin' && password === 'admin123') {
        // 登录成功
        if (remember) {
          localStorage.setItem('rememberLogin', 'true');
          localStorage.setItem('username', username);
        }
        
        // 保存登录状态
        sessionStorage.setItem('isLoggedIn', 'true');
        sessionStorage.setItem('currentUser', username);
        
        // 跳转到仪表板
        window.location.href = 'dashboard.html';
      } else {
        // 登录失败
        showError('用户名或密码错误，请重试');
      }
    }
    
    function showError(message) {
      const errorDiv = document.getElementById('errorMessage');
      errorDiv.textContent = message;
      errorDiv.style.display = 'block';
      
      // 3秒后自动隐藏错误信息
      setTimeout(() => {
        errorDiv.style.display = 'none';
      }, 3000);
    }
    
    function showForgotPassword() {
      alert('请联系系统管理员重置密码\n邮箱: <EMAIL>\n电话: ************');
    }
    
    // 页面加载时检查是否有记住的登录信息
    document.addEventListener('DOMContentLoaded', function() {
      if (localStorage.getItem('rememberLogin') === 'true') {
        document.getElementById('username').value = localStorage.getItem('username') || '';
        document.getElementById('remember').checked = true;
      }
      
      // 如果已经登录，直接跳转到仪表板
      if (sessionStorage.getItem('isLoggedIn') === 'true') {
        window.location.href = 'dashboard.html';
      }
    });
  </script>
</body>
</html>
