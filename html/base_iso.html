<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>基础 ISO 镜像列表</title>
  <style>
    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }
    .header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
    }
    .action-link {
      color: #2196F3;
      text-decoration: none;
      margin: 0 5px;
    }
    .btn {
      padding: 8px 16px;
      background-color: #3f51b5;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
      transition: background-color 0.3s;
    }
    .btn:hover {
      background-color: #303f9f;
    }
    .btn-success {
      background-color: #4CAF50;
    }
    .btn-success:hover {
      background-color: #388E3C;
    }
    .btn-warning {
      background-color: #FF9800;
    }
    .btn-warning:hover {
      background-color: #F57C00;
    }
    .btn-danger {
      background-color: #F44336;
    }
    .btn-danger:hover {
      background-color: #D32F2F;
    }
    .back-btn {
      padding: 8px 15px;
      background-color: #2196F3;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
    }
    table {
      width: 100%;
      border-collapse: collapse;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
      border-radius: 5px;
      overflow: hidden;
    }
    th {
      background-color: #3f51b5;
      color: white;
      padding: 12px 15px;
      text-align: left;
      font-weight: bold;
      font-size: 14px;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      border-bottom: 2px solid #ddd;
    }
    td {
      padding: 10px 15px;
      text-align: left;
      border-bottom: 1px solid #ddd;
    }
    tr:hover {
      background-color: #f5f5f5;
    }
    .modal {
      display: none;
      position: fixed;
      z-index: 1;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0,0,0,0.4);
    }
    .modal-content {
      background-color: #fefefe;
      margin: 15% auto;
      padding: 20px;
      border: 1px solid #888;
      width: 50%;
      border-radius: 5px;
    }
    .close {
      color: #aaa;
      float: right;
      font-size: 28px;
      font-weight: bold;
      cursor: pointer;
    }
    .close:hover {
      color: black;
    }
    .form-group {
      margin-bottom: 15px;
    }
    .form-group label {
      display: block;
      margin-bottom: 5px;
      font-weight: bold;
    }
    .form-group input, 
    .form-group select, 
    .form-group textarea {
      width: 100%;
      padding: 8px;
      border: 1px solid #ddd;
      border-radius: 4px;
      box-sizing: border-box;
    }
    .form-buttons {
      display: flex;
      justify-content: flex-end;
      gap: 10px;
      margin-top: 20px;
    }
    .search-bar {
      display: flex;
      gap: 10px;
      margin-bottom: 20px;
    }
    .search-bar input {
      padding: 8px;
      width: 300px;
    }
    .search-bar button {
      padding: 8px 15px;
      background-color: #4CAF50;
      color: white;
      border: none;
      cursor: pointer;
    }
    .action-buttons {
      margin-bottom: 20px;
    }
    .action-buttons button {
      padding: 8px 15px;
      margin-right: 10px;
      cursor: pointer;
    }
    .add-button {
      background-color: #4CAF50;
      color: white;
      border: none;
    }
    .edit-btn {
      background: #FFC107;
      border: none;
      padding: 3px 8px;
      border-radius: 3px;
      cursor: pointer;
      margin: 0 5px;
    }
    .delete-btn {
      background: #F44336;
      color: white;
      border: none;
      padding: 3px 8px;
      border-radius: 3px;
      cursor: pointer;
      margin-left: 5px;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>📂 基础 ISO 镜像列表</h1>
      <div>
        <button onclick="location.href='/html/project.html'" class="back-btn">返回项目列表</button>
      </div>
    </div>
    
    <div class="search-bar">
      <input type="text" id="searchInput" placeholder="搜索操作系统、架构、版本...">
      <button onclick="searchImages()">搜索</button>
    </div>
    
    <div class="action-buttons">
      <button class="add-button" onclick="openAddModal()">添加基础镜像</button>
    </div>
    
    <p>以下是当前平台支持的系统基础镜像，支持项目打包和 ISO 构建选择。</p>

    <table>
      <thead>
        <tr>
          <th>操作系统</th>
          <th>架构</th>
          <th>版本</th>
          <th>镜像文件</th>
          <th>更新时间</th>
          <th>说明</th>
          <th>操作</th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td>Anolis</td>
          <td>x86_64</td>
          <td>8.8-minimal</td>
          <td>anolis-8.8-min.iso</td>
          <td>2025-05-20</td>
          <td>最小系统，仅包含基础包</td>
          <td>
            <a href="/isos/anolis-8.8-min.iso" class="action-link">下载</a> | 
            <button onclick="openEditModal('Anolis', 'x86_64', '8.8-minimal', 'anolis-8.8-min.iso', '2025-05-20', '最小系统，仅包含基础包')" class="edit-btn">编辑</button> | 
            <button onclick="confirmDelete('anolis-8.8-min.iso')" class="delete-btn">删除</button>
          </td>
        </tr>
        <tr>
          <td>Kylin</td>
          <td>ARM64</td>
          <td>V10-SP3</td>
          <td>kylin-v10-sp3-arm.iso</td>
          <td>2025-05-18</td>
          <td>适配 ARM 设备，带 GUI</td>
          <td>
            <a href="/isos/kylin-v10-sp3-arm.iso" class="action-link">下载</a> | 
            <button onclick="openEditModal('Kylin', 'ARM64', 'V10-SP3', 'kylin-v10-sp3-arm.iso', '2025-05-18', '适配 ARM 设备，带 GUI')" class="edit-btn">编辑</button> | 
            <button onclick="confirmDelete('kylin-v10-sp3-arm.iso')" class="delete-btn">删除</button>
          </td>
        </tr>
        <tr>
          <td>Anolis</td>
          <td>ARM64</td>
          <td>8.6-custom</td>
          <td>anolis-8.6-arm.iso</td>
          <td>2025-04-30</td>
          <td>定制系统，适配调度平台</td>
          <td>
            <a href="/isos/anolis-8.6-arm.iso" class="action-link">下载</a> | 
            <button onclick="openEditModal('Anolis', 'ARM64', '8.6-custom', 'anolis-8.6-arm.iso', '2025-04-30', '定制系统，适配调度平台')" class="edit-btn">编辑</button> | 
            <button onclick="confirmDelete('anolis-8.6-arm.iso')" class="delete-btn">删除</button>
          </td>
        </tr>
      </tbody>
    </table>
  </div>

  <!-- 添加镜像弹窗 -->
  <div id="addModal" class="modal">
    <div class="modal-content">
      <span class="close" onclick="closeAddModal()">&times;</span>
      <h2>添加基础镜像</h2>
      <form id="addForm">
        <div class="form-group">
          <label for="os">操作系统:</label>
          <input type="text" id="os" name="os" required>
        </div>
        <div class="form-group">
          <label for="arch">架构:</label>
          <select id="arch" name="arch" required>
            <option value="x86_64">x86_64</option>
            <option value="ARM64">ARM64</option>
            <option value="MIPS">MIPS</option>
          </select>
        </div>
        <div class="form-group">
          <label for="version">版本:</label>
          <input type="text" id="version" name="version" required>
        </div>
        <div class="form-group">
          <label for="filename">镜像文件:</label>
          <input type="text" id="filename" name="filename" required>
        </div>
        <div class="form-group">
          <label for="date">更新时间:</label>
          <input type="text" id="date" name="date" value="2025-05-22" required>
        </div>
        <div class="form-group">
          <label for="description">说明:</label>
          <textarea id="description" name="description" rows="3" required></textarea>
        </div>
        <div class="form-group">
          <label for="file">上传 ISO 文件:</label>
          <input type="file" id="file" name="file" accept=".iso">
        </div>
        <button type="button" class="btn btn-success" onclick="addImage()">提交</button>
      </form>
    </div>
  </div>

  <!-- 编辑镜像弹窗 -->
  <div id="editModal" class="modal">
    <div class="modal-content">
      <span class="close" onclick="closeEditModal()">&times;</span>
      <h2>编辑基础镜像</h2>
      <form id="editForm">
        <input type="hidden" id="edit-original-filename" name="original-filename">
        <div class="form-group">
          <label for="edit-os">操作系统:</label>
          <input type="text" id="edit-os" name="os" required>
        </div>
        <div class="form-group">
          <label for="edit-arch">架构:</label>
          <select id="edit-arch" name="arch" required>
            <option value="x86_64">x86_64</option>
            <option value="ARM64">ARM64</option>
            <option value="MIPS">MIPS</option>
          </select>
        </div>
        <div class="form-group">
          <label for="edit-version">版本:</label>
          <input type="text" id="edit-version" name="version" required>
        </div>
        <div class="form-group">
          <label for="edit-filename">镜像文件:</label>
          <input type="text" id="edit-filename" name="filename" required>
        </div>
        <div class="form-group">
          <label for="edit-date">更新时间:</label>
          <input type="text" id="edit-date" name="date" required>
        </div>
        <div class="form-group">
          <label for="edit-description">说明:</label>
          <textarea id="edit-description" name="description" rows="3" required></textarea>
        </div>
        <button type="button" class="btn btn-warning" onclick="updateImage()">更新</button>
      </form>
    </div>
  </div>

  <script>
    // 搜索镜像
    function searchImages() {
      const searchValue = document.getElementById('searchInput').value.toLowerCase();
      const rows = document.querySelectorAll('tbody tr');
      
      rows.forEach(row => {
        const os = row.querySelector('td:nth-child(1)').textContent.toLowerCase();
        const arch = row.querySelector('td:nth-child(2)').textContent.toLowerCase();
        const version = row.querySelector('td:nth-child(3)').textContent.toLowerCase();
        const filename = row.querySelector('td:nth-child(4)').textContent.toLowerCase();
        const description = row.querySelector('td:nth-child(6)').textContent.toLowerCase();
        
        if (os.includes(searchValue) || 
            arch.includes(searchValue) || 
            version.includes(searchValue) || 
            filename.includes(searchValue) || 
            description.includes(searchValue)) {
          row.style.display = '';
        } else {
          row.style.display = 'none';
        }
      });
    }
  
    // 添加镜像相关函数
    function openAddModal() {
      document.getElementById('addModal').style.display = 'block';
    }
    
    function closeAddModal() {
      document.getElementById('addModal').style.display = 'none';
      document.getElementById('addForm').reset();
    }
    
    function addImage() {
      // 获取表单数据
      const os = document.getElementById('os').value;
      const arch = document.getElementById('arch').value;
      const version = document.getElementById('version').value;
      const filename = document.getElementById('filename').value;
      const date = document.getElementById('date').value;
      const description = document.getElementById('description').value;
      
      if (!os || !arch || !version || !filename || !date || !description) {
        alert('请填写所有必填字段');
        return;
      }
      
      // 向后端发送请求，这里只做模拟
      alert('新基础镜像添加成功: ' + filename);
      
      // 添加新行到表格中
      const table = document.querySelector('tbody');
      const newRow = document.createElement('tr');
      newRow.innerHTML = `
        <td>${os}</td>
        <td>${arch}</td>
        <td>${version}</td>
        <td>${filename}</td>
        <td>${date}</td>
        <td>${description}</td>
        <td>
          <a href="/isos/${filename}" class="action-link">下载</a>
          <a href="#" class="action-link" onclick="openEditModal('${os}', '${arch}', '${version}', '${filename}', '${date}', '${description}')">编辑</a>
          <a href="#" class="action-link" onclick="confirmDelete('${filename}')">删除</a>
        </td>
      `;
      table.appendChild(newRow);
      
      // 关闭弹窗
      closeAddModal();
    }
    
    // 编辑镜像相关函数
    function openEditModal(os, arch, version, filename, date, description) {
      document.getElementById('edit-original-filename').value = filename;
      document.getElementById('edit-os').value = os;
      document.getElementById('edit-arch').value = arch;
      document.getElementById('edit-version').value = version;
      document.getElementById('edit-filename').value = filename;
      document.getElementById('edit-date').value = date;
      document.getElementById('edit-description').value = description;
      document.getElementById('editModal').style.display = 'block';
    }
    
    function closeEditModal() {
      document.getElementById('editModal').style.display = 'none';
    }
    
    function updateImage() {
      // 获取表单数据
      const originalFilename = document.getElementById('edit-original-filename').value;
      const os = document.getElementById('edit-os').value;
      const arch = document.getElementById('edit-arch').value;
      const version = document.getElementById('edit-version').value;
      const filename = document.getElementById('edit-filename').value;
      const date = document.getElementById('edit-date').value;
      const description = document.getElementById('edit-description').value;
      
      if (!os || !arch || !version || !filename || !date || !description) {
        alert('请填写所有必填字段');
        return;
      }
      
      // 向后端发送请求，这里只做模拟
      alert('基础镜像更新成功: ' + filename);
      
      // 查找并更新表格行
      const rows = document.querySelectorAll('tbody tr');
      for (let i = 0; i < rows.length; i++) {
        const cells = rows[i].getElementsByTagName('td');
        if (cells[3].innerText === originalFilename) {
          cells[0].innerText = os;
          cells[1].innerText = arch;
          cells[2].innerText = version;
          cells[3].innerText = filename;
          cells[4].innerText = date;
          cells[5].innerText = description;
          // 更新操作列
          cells[6].innerHTML = `
            <a href="/isos/${filename}" class="action-link">下载</a>
            <a href="#" class="action-link" onclick="openEditModal('${os}', '${arch}', '${version}', '${filename}', '${date}', '${description}')">编辑</a>
            <a href="#" class="action-link" onclick="confirmDelete('${filename}')">删除</a>
          `;
          break;
        }
      }
      
      // 关闭弹窗
      closeEditModal();
    }
    
    // 删除镜像函数
    function confirmDelete(filename) {
      if (confirm('确定要删除基础镜像 ' + filename + ' 吗？')) {
        // 向后端发送请求，这里只做模拟
        alert('基础镜像已删除: ' + filename);
        
        // 从表格中移除该行
        const rows = document.querySelectorAll('tbody tr');
        for (let i = 0; i < rows.length; i++) {
          const cells = rows[i].getElementsByTagName('td');
          if (cells[3].innerText === filename) {
            rows[i].remove();
            break;
          }
        }
      }
    }
  </script>
</body>
</html>