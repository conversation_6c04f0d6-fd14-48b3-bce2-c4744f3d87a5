<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <title>项目模块 - 管理页</title>
  <style>
    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }
    .header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
    }
    .search-bar {
      display: flex;
      gap: 10px;
      margin-bottom: 20px;
    }
    .search-bar input {
      padding: 8px;
      width: 300px;
    }
    .git-url {
      max-width: 250px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .search-bar button {
      padding: 8px 15px;
      background-color: #4CAF50;
      color: white;
      border: none;
      cursor: pointer;
    }
    .action-buttons {
      margin-bottom: 20px;
    }
    .action-buttons button {
      padding: 8px 15px;
      margin-right: 10px;
      cursor: pointer;
    }
    .add-button {
      background-color: #4CAF50;
      color: white;
      border: none;
    }
    .back-btn {
      padding: 8px 15px;
      background-color: #2196F3;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
    }
    .action-link {
      color: #2196F3;
      text-decoration: none;
      margin: 0 5px;
    }
    .edit-btn {
      background: #FFC107;
      border: none;
      padding: 3px 8px;
      border-radius: 3px;
      cursor: pointer;
      margin: 0 5px;
    }
    .delete-btn {
      background: #F44336;
      color: white;
      border: none;
      padding: 3px 8px;
      border-radius: 3px;
      cursor: pointer;
      margin-left: 5px;
    }
    table {
      width: 100%;
      border-collapse: collapse;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
      border-radius: 5px;
      overflow: hidden;
    }
    th {
      background-color: #3f51b5;
      color: white;
      padding: 12px 15px;
      text-align: left;
      font-weight: bold;
      font-size: 14px;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      border-bottom: 2px solid #ddd;
    }
    td {
      padding: 10px 15px;
      text-align: left;
      border-bottom: 1px solid #ddd;
    }
    tr:hover {
      background-color: #f5f5f5;
    }
    .modal {
      display: none;
      position: fixed;
      z-index: 1;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0,0,0,0.4);
    }
    .modal-content {
      background-color: #fefefe;
      margin: 15% auto;
      padding: 20px;
      border: 1px solid #888;
      width: 50%;
    }
    .form-group {
      margin-bottom: 15px;
    }
    .form-group label {
      display: block;
      margin-bottom: 5px;
    }
    .form-group input {
      width: 100%;
      padding: 8px;
    }
    .form-buttons {
      display: flex;
      justify-content: flex-end;
      gap: 10px;
      margin-top: 20px;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>📁 项目模块 - 超融合 3.2</h1>
      <div>
        <button onclick="location.href='/html/project.html'" class="back-btn">返回项目列表</button>
      </div>
    </div>

    <div class="search-bar">
      <input type="text" id="searchInput" placeholder="搜索模块名称、Git地址、分支或Commit ID...">
      <button onclick="searchModules()">搜索</button>
    </div>

    <div class="action-buttons">
      <button class="add-button" onclick="openAddModal()">添加模块</button>
    </div>

    <table>
      <thead>
        <tr>
          <th>模块名称</th>
          <th>Git 地址</th>
          <th>分支</th>
          <th>Tag</th>
          <th>Commit ID</th>
          <th>操作</th>
        </tr>
      </thead>
      <tbody id="moduleTableBody">
        <tr>
          <td>theweb</td>
          <td>https://github.com/example/theweb.git</td>
          <td>master</td>
          <td>v1.2.1</td>
          <td>8a3df64</td>
          <td>
            <a href="/project/my-app/frontend/tag" class="action-link">打包</a> | 
            <a href="/project/my-app/frontend/history" class="action-link">历史</a> | 
            <button onclick="openEditModal('theweb', 'https://github.com/example/theweb.git', 'master', 'v1.2.1', '8a3df64')" class="edit-btn">编辑</button> | 
            <button onclick="deleteModule('theweb')" class="delete-btn">删除</button>
          </td>
        </tr>
        <tr>
          <td>theapi</td>
          <td>https://github.com/example/theapi.git</td>
          <td>develop</td>
          <td>v1.2.3</td>
          <td>42b7e9a</td>
          <td>
            <a href="/project/my-app/api/tag" class="action-link">打包</a> | 
            <a href="/project/my-app/api/history" class="action-link">历史</a> | 
            <button onclick="openEditModal('theapi', 'https://github.com/example/theapi.git', 'develop', 'v1.2.3', '42b7e9a')" class="edit-btn">编辑</button> | 
            <button onclick="deleteModule('theapi')" class="delete-btn">删除</button>
          </td>
        </tr>
      </tbody>
    </table>
  </div>

  <!-- 添加模块的模态框 -->
  <div id="addModuleModal" class="modal">
    <div class="modal-content">
      <h2>添加新模块</h2>
      <div class="form-group">
        <label for="newModuleName">模块名称:</label>
        <input type="text" id="newModuleName">
      </div>
      <div class="form-group">
        <label for="newModuleGitUrl">Git 地址:</label>
        <input type="text" id="newModuleGitUrl">
      </div>
      <div class="form-group">
        <label for="newModuleBranch">分支:</label>
        <input type="text" id="newModuleBranch" value="master">
      </div>
      <div class="form-group">
        <label for="newModuleTag">初始Tag:</label>
        <input type="text" id="newModuleTag">
      </div>
      <div class="form-group">
        <label for="newModuleCommitId">Commit ID:</label>
        <input type="text" id="newModuleCommitId">
      </div>
      <div class="form-buttons">
        <button onclick="closeAddModal()">取消</button>
        <button onclick="addModule()">保存</button>
      </div>
    </div>
  </div>

  <!-- 编辑模块的模态框 -->
  <div id="editModuleModal" class="modal">
    <div class="modal-content">
      <h2>编辑模块</h2>
      <div class="form-group">
        <label for="editModuleName">模块名称:</label>
        <input type="text" id="editModuleName">
      </div>
      <div class="form-group">
        <label for="editModuleGitUrl">Git 地址:</label>
        <input type="text" id="editModuleGitUrl">
      </div>
      <div class="form-group">
        <label for="editModuleBranch">分支:</label>
        <input type="text" id="editModuleBranch">
      </div>
      <div class="form-group">
        <label for="editModuleTag">当前Tag:</label>
        <input type="text" id="editModuleTag">
      </div>
      <div class="form-group">
        <label for="editModuleCommitId">Commit ID:</label>
        <input type="text" id="editModuleCommitId">
      </div>
      <input type="hidden" id="originalModuleName">
      <div class="form-buttons">
        <button onclick="closeEditModal()">取消</button>
        <button onclick="updateModule()">更新</button>
      </div>
    </div>
  </div>

  <script>
    // 搜索模块
    function searchModules() {
      const searchValue = document.getElementById('searchInput').value.toLowerCase();
      const rows = document.querySelectorAll('#moduleTableBody tr');
      
      rows.forEach(row => {
        const moduleName = row.querySelector('td:first-child').textContent.toLowerCase();
        const gitUrl = row.querySelector('td:nth-child(2)').textContent.toLowerCase();
        const branch = row.querySelector('td:nth-child(3)').textContent.toLowerCase();
        const commitId = row.querySelector('td:nth-child(5)').textContent.toLowerCase();
        if (moduleName.includes(searchValue) || 
            gitUrl.includes(searchValue) || 
            branch.includes(searchValue) || 
            commitId.includes(searchValue)) {
          row.style.display = '';
        } else {
          row.style.display = 'none';
        }
      });
    }

    // 打开添加模块的模态框
    function openAddModal() {
      document.getElementById('addModuleModal').style.display = 'block';
      document.getElementById('newModuleName').value = '';
      document.getElementById('newModuleGitUrl').value = '';
      document.getElementById('newModuleBranch').value = 'master';
      document.getElementById('newModuleTag').value = '';
      document.getElementById('newModuleCommitId').value = '';
    }

    // 关闭添加模块的模态框
    function closeAddModal() {
      document.getElementById('addModuleModal').style.display = 'none';
    }

    // 添加新模块
    function addModule() {
      const moduleName = document.getElementById('newModuleName').value.trim();
      const gitUrl = document.getElementById('newModuleGitUrl').value.trim();
      const branch = document.getElementById('newModuleBranch').value.trim() || 'master';
      const moduleTag = document.getElementById('newModuleTag').value.trim();
      const commitId = document.getElementById('newModuleCommitId').value.trim();
      
      if (moduleName && gitUrl && moduleTag) {
        const tableBody = document.getElementById('moduleTableBody');
        const newRow = document.createElement('tr');
        
        newRow.innerHTML = `
          <td>${moduleName}</td>
          <td class="git-url">${gitUrl}</td>
          <td>${branch}</td>
          <td>${moduleTag}</td>
          <td>${commitId}</td>
          <td>
            <a href="/project/my-app/frontend/tag" class="action-link">打包</a> | 
            <a href="/project/my-app/frontend/history" class="action-link">历史</a> | 
            <button onclick="openEditModal('${moduleName}', '${gitUrl}', '${branch}', '${moduleTag}', '${commitId}')" class="edit-btn">编辑</button> | 
            <button onclick="deleteModule('${moduleName}')" class="delete-btn">删除</button>
          </td>
        `;
        
        tableBody.appendChild(newRow);
        closeAddModal();
      } else {
        alert('请填写所有必填字段');
      }
    }

    // 打开编辑模块的模态框
    function openEditModal(moduleName, gitUrl, branch, moduleTag, commitId) {
      document.getElementById('editModuleModal').style.display = 'block';
      document.getElementById('editModuleName').value = moduleName;
      document.getElementById('editModuleGitUrl').value = gitUrl;
      document.getElementById('editModuleBranch').value = branch;
      document.getElementById('editModuleTag').value = moduleTag;
      document.getElementById('editModuleCommitId').value = commitId;
      document.getElementById('originalModuleName').value = moduleName;
    }

    // 关闭编辑模块的模态框
    function closeEditModal() {
      document.getElementById('editModuleModal').style.display = 'none';
    }

    // 更新模块信息
    function updateModule() {
      const originalName = document.getElementById('originalModuleName').value;
      const newName = document.getElementById('editModuleName').value.trim();
      const gitUrl = document.getElementById('editModuleGitUrl').value.trim();
      const branch = document.getElementById('editModuleBranch').value.trim();
      const newTag = document.getElementById('editModuleTag').value.trim();
      const commitId = document.getElementById('editModuleCommitId').value.trim();
      
      if (newName && gitUrl && branch && newTag) {
        const rows = document.querySelectorAll('#moduleTableBody tr');
        
        rows.forEach(row => {
          const moduleName = row.querySelector('td:first-child').textContent;
          if (moduleName === originalName) {
            row.querySelector('td:first-child').textContent = newName;
            row.querySelector('td:nth-child(2)').textContent = gitUrl;
            row.querySelector('td:nth-child(3)').textContent = branch;
            row.querySelector('td:nth-child(4)').textContent = newTag;
            row.querySelector('td:nth-child(5)').textContent = commitId;
            
            // 更新操作按钮的事件处理程序
            const td = row.querySelector('td:last-child');
            td.innerHTML = `
              <a href="/project/my-app/frontend/tag" class="action-link">打包</a> | 
              <a href="/project/my-app/frontend/history" class="action-link">历史</a> | 
              <button onclick="openEditModal('${newName}', '${gitUrl}', '${branch}', '${newTag}', '${commitId}')" class="edit-btn">编辑</button> | 
              <button onclick="deleteModule('${newName}')" class="delete-btn">删除</button>
            `;
          }
        });
        
        closeEditModal();
      } else {
        alert('请填写所有必填字段');
      }
    }

    // 删除模块
    function deleteModule(moduleName) {
      if (confirm(`确定要删除模块 "${moduleName}" 吗?`)) {
        const rows = document.querySelectorAll('#moduleTableBody tr');
        
        rows.forEach(row => {
          const name = row.querySelector('td:first-child').textContent;
          if (name === moduleName) {
            row.remove();
          }
        });
      }
    }
  </script>
</body>
</html>