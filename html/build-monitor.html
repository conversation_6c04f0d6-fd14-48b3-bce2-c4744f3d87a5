<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>构建监控 - 自动打包平台</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background: #f5f7fa;
      color: #333;
    }

    .header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 1rem 2rem;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }

    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      max-width: 1400px;
      margin: 0 auto;
    }

    .header-title {
      display: flex;
      align-items: center;
      gap: 12px;
    }

    .header-title h1 {
      font-size: 24px;
      font-weight: 600;
    }

    .back-btn {
      background: rgba(255,255,255,0.2);
      border: 1px solid rgba(255,255,255,0.3);
      color: white;
      padding: 8px 16px;
      border-radius: 6px;
      cursor: pointer;
      transition: all 0.3s ease;
      text-decoration: none;
    }

    .back-btn:hover {
      background: rgba(255,255,255,0.3);
    }

    .container {
      max-width: 1400px;
      margin: 0 auto;
      padding: 2rem;
    }

    .monitor-controls {
      background: white;
      border-radius: 12px;
      padding: 1.5rem;
      margin-bottom: 2rem;
      box-shadow: 0 2px 10px rgba(0,0,0,0.05);
      border: 1px solid #e1e5e9;
    }

    .controls-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      gap: 1rem;
      flex-wrap: wrap;
    }

    .filter-group {
      display: flex;
      align-items: center;
      gap: 1rem;
    }

    .filter-select {
      padding: 8px 12px;
      border: 1px solid #e1e5e9;
      border-radius: 6px;
      background: white;
      font-size: 14px;
    }

    .refresh-btn {
      background: linear-gradient(135deg, #667eea, #764ba2);
      color: white;
      border: none;
      padding: 10px 20px;
      border-radius: 6px;
      cursor: pointer;
      font-size: 14px;
      font-weight: 500;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .refresh-btn:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
    }

    .auto-refresh {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 14px;
      color: #666;
    }

    .auto-refresh input[type="checkbox"] {
      width: auto;
    }

    .builds-grid {
      display: grid;
      gap: 1.5rem;
    }

    .build-card {
      background: white;
      border-radius: 12px;
      padding: 1.5rem;
      box-shadow: 0 2px 10px rgba(0,0,0,0.05);
      border: 1px solid #e1e5e9;
      transition: all 0.3s ease;
    }

    .build-card:hover {
      box-shadow: 0 4px 20px rgba(0,0,0,0.1);
    }

    .build-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 1rem;
    }

    .build-info h3 {
      font-size: 18px;
      font-weight: 600;
      color: #333;
      margin-bottom: 4px;
    }

    .build-meta {
      font-size: 14px;
      color: #666;
    }

    .build-status {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 6px 12px;
      border-radius: 20px;
      font-size: 12px;
      font-weight: 500;
    }

    .status-building {
      background: #fff3e0;
      color: #f57c00;
    }

    .status-success {
      background: #e8f5e8;
      color: #2e7d32;
    }

    .status-failed {
      background: #ffebee;
      color: #c62828;
    }

    .status-queued {
      background: #f3e5f5;
      color: #7b1fa2;
    }

    .progress-section {
      margin: 1rem 0;
    }

    .progress-bar {
      width: 100%;
      height: 8px;
      background: #e1e5e9;
      border-radius: 4px;
      overflow: hidden;
      margin-bottom: 8px;
    }

    .progress-fill {
      height: 100%;
      background: linear-gradient(90deg, #667eea, #764ba2);
      border-radius: 4px;
      transition: width 0.3s ease;
    }

    .progress-text {
      display: flex;
      justify-content: space-between;
      font-size: 12px;
      color: #666;
    }

    .build-details {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
      gap: 1rem;
      margin: 1rem 0;
      padding: 1rem;
      background: #f8f9fa;
      border-radius: 8px;
    }

    .detail-item {
      text-align: center;
    }

    .detail-label {
      font-size: 12px;
      color: #666;
      margin-bottom: 4px;
    }

    .detail-value {
      font-size: 14px;
      font-weight: 500;
      color: #333;
    }

    .build-actions {
      display: flex;
      gap: 8px;
      margin-top: 1rem;
    }

    .action-btn {
      padding: 6px 12px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 12px;
      font-weight: 500;
      transition: all 0.3s ease;
    }

    .btn-primary {
      background: #667eea;
      color: white;
    }

    .btn-secondary {
      background: #6c757d;
      color: white;
    }

    .btn-danger {
      background: #dc3545;
      color: white;
    }

    .action-btn:hover {
      transform: translateY(-1px);
      box-shadow: 0 2px 8px rgba(0,0,0,0.2);
    }

    .log-output {
      background: #1e1e1e;
      color: #f8f8f2;
      padding: 1rem;
      border-radius: 8px;
      font-family: 'Courier New', monospace;
      font-size: 12px;
      max-height: 200px;
      overflow-y: auto;
      margin-top: 1rem;
      display: none;
    }

    .log-output.show {
      display: block;
    }

    .log-line {
      margin-bottom: 2px;
      white-space: pre-wrap;
    }

    .log-timestamp {
      color: #6272a4;
    }

    .log-info {
      color: #8be9fd;
    }

    .log-warning {
      color: #f1fa8c;
    }

    .log-error {
      color: #ff5555;
    }

    .empty-state {
      text-align: center;
      padding: 3rem;
      color: #666;
    }

    .empty-state-icon {
      font-size: 48px;
      margin-bottom: 1rem;
      opacity: 0.5;
    }

    @media (max-width: 768px) {
      .container {
        padding: 1rem;
      }

      .controls-row {
        flex-direction: column;
        align-items: stretch;
      }

      .filter-group {
        justify-content: space-between;
      }

      .build-details {
        grid-template-columns: 1fr 1fr;
      }

      .build-actions {
        flex-wrap: wrap;
      }
    }
  </style>
</head>
<body>
  <header class="header">
    <div class="header-content">
      <div class="header-title">
        <span>📊</span>
        <h1>构建监控</h1>
      </div>
      <a href="dashboard.html" class="back-btn">返回仪表板</a>
    </div>
  </header>

  <div class="container">
    <div class="monitor-controls">
      <div class="controls-row">
        <div class="filter-group">
          <select class="filter-select" id="statusFilter" onchange="filterBuilds()">
            <option value="all">全部状态</option>
            <option value="building">构建中</option>
            <option value="queued">队列中</option>
            <option value="success">成功</option>
            <option value="failed">失败</option>
          </select>

          <select class="filter-select" id="projectFilter" onchange="filterBuilds()">
            <option value="all">全部项目</option>
            <option value="hyperconverged">超融合3.2</option>
            <option value="clouddesktop">云桌面2.0</option>
          </select>
        </div>

        <div class="filter-group">
          <label class="auto-refresh">
            <input type="checkbox" id="autoRefresh" onchange="toggleAutoRefresh()">
            自动刷新
          </label>

          <button class="refresh-btn" onclick="refreshBuilds()">
            <span>🔄</span>
            刷新
          </button>
        </div>
      </div>
    </div>

    <div class="builds-grid" id="buildsContainer">
      <!-- 构建任务将通过JavaScript动态加载 -->
    </div>

    <div class="empty-state" id="emptyState" style="display: none;">
      <div class="empty-state-icon">🔍</div>
      <h3>暂无构建任务</h3>
      <p>当前没有符合条件的构建任务</p>
    </div>
  </div>

  <script>
    // 模拟构建数据
    let builds = [
      {
        id: 'build-001',
        project: '超融合3.2',
        version: 'v3.2.1',
        status: 'building',
        progress: 65,
        startTime: new Date(Date.now() - 15 * 60 * 1000),
        estimatedTime: '5分钟',
        arch: 'ARM',
        os: 'Anolis',
        modules: ['frontend', 'api', 'scheduler'],
        user: 'alice'
      },
      {
        id: 'build-002',
        project: '云桌面2.0',
        version: 'v2.0.24',
        status: 'queued',
        progress: 0,
        startTime: null,
        estimatedTime: '等待中',
        arch: 'x86',
        os: 'Kylin',
        modules: ['frontend', 'api'],
        user: 'bob'
      },
      {
        id: 'build-003',
        project: '超融合3.2',
        version: 'v3.2.0',
        status: 'success',
        progress: 100,
        startTime: new Date(Date.now() - 2 * 60 * 60 * 1000),
        estimatedTime: '已完成',
        arch: 'ARM',
        os: 'Anolis',
        modules: ['frontend', 'api'],
        user: 'charlie'
      }
    ];

    let autoRefreshInterval = null;

    // 渲染构建列表
    function renderBuilds() {
      const container = document.getElementById('buildsContainer');
      const emptyState = document.getElementById('emptyState');

      const statusFilter = document.getElementById('statusFilter').value;
      const projectFilter = document.getElementById('projectFilter').value;

      let filteredBuilds = builds.filter(build => {
        const statusMatch = statusFilter === 'all' || build.status === statusFilter;
        const projectMatch = projectFilter === 'all' ||
          (projectFilter === 'hyperconverged' && build.project === '超融合3.2') ||
          (projectFilter === 'clouddesktop' && build.project === '云桌面2.0');
        return statusMatch && projectMatch;
      });

      if (filteredBuilds.length === 0) {
        container.innerHTML = '';
        emptyState.style.display = 'block';
        return;
      }

      emptyState.style.display = 'none';

      container.innerHTML = filteredBuilds.map(build => `
        <div class="build-card" data-build-id="${build.id}">
          <div class="build-header">
            <div class="build-info">
              <h3>${build.project} - ${build.version}</h3>
              <div class="build-meta">
                构建者: ${build.user} • ${build.arch} • ${build.os}
                ${build.startTime ? ' • 开始时间: ' + formatTime(build.startTime) : ''}
              </div>
            </div>
            <div class="build-status status-${build.status}">
              ${getStatusIcon(build.status)} ${getStatusText(build.status)}
            </div>
          </div>

          ${build.status === 'building' ? `
            <div class="progress-section">
              <div class="progress-bar">
                <div class="progress-fill" style="width: ${build.progress}%"></div>
              </div>
              <div class="progress-text">
                <span>进度: ${build.progress}%</span>
                <span>预计剩余: ${build.estimatedTime}</span>
              </div>
            </div>
          ` : ''}

          <div class="build-details">
            <div class="detail-item">
              <div class="detail-label">包含模块</div>
              <div class="detail-value">${build.modules.length} 个</div>
            </div>
            <div class="detail-item">
              <div class="detail-label">架构</div>
              <div class="detail-value">${build.arch}</div>
            </div>
            <div class="detail-item">
              <div class="detail-label">操作系统</div>
              <div class="detail-value">${build.os}</div>
            </div>
            <div class="detail-item">
              <div class="detail-label">状态</div>
              <div class="detail-value">${getStatusText(build.status)}</div>
            </div>
          </div>

          <div class="build-actions">
            <button class="action-btn btn-primary" onclick="showLogs('${build.id}')">查看日志</button>
            ${build.status === 'building' ? `
              <button class="action-btn btn-danger" onclick="cancelBuild('${build.id}')">取消构建</button>
            ` : ''}
            ${build.status === 'success' ? `
              <button class="action-btn btn-secondary" onclick="downloadISO('${build.id}')">下载ISO</button>
            ` : ''}
            ${build.status === 'failed' ? `
              <button class="action-btn btn-primary" onclick="retryBuild('${build.id}')">重新构建</button>
            ` : ''}
          </div>

          <div class="log-output" id="logs-${build.id}">
            <div class="log-line"><span class="log-timestamp">[${formatTime(new Date())}]</span> <span class="log-info">构建日志加载中...</span></div>
          </div>
        </div>
      `).join('');
    }

    // 获取状态图标
    function getStatusIcon(status) {
      const icons = {
        building: '🔨',
        queued: '⏳',
        success: '✅',
        failed: '❌'
      };
      return icons[status] || '❓';
    }

    // 获取状态文本
    function getStatusText(status) {
      const texts = {
        building: '构建中',
        queued: '队列中',
        success: '成功',
        failed: '失败'
      };
      return texts[status] || '未知';
    }

    // 格式化时间
    function formatTime(date) {
      return date.toLocaleString('zh-CN', {
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      });
    }

    // 筛选构建
    function filterBuilds() {
      renderBuilds();
    }

    // 刷新构建列表
    function refreshBuilds() {
      // 模拟更新构建进度
      builds.forEach(build => {
        if (build.status === 'building' && build.progress < 100) {
          build.progress = Math.min(100, build.progress + Math.random() * 10);
          if (build.progress >= 100) {
            build.status = Math.random() > 0.2 ? 'success' : 'failed';
          }
        }
      });

      renderBuilds();
    }

    // 切换自动刷新
    function toggleAutoRefresh() {
      const checkbox = document.getElementById('autoRefresh');

      if (checkbox.checked) {
        autoRefreshInterval = setInterval(refreshBuilds, 5000);
      } else {
        if (autoRefreshInterval) {
          clearInterval(autoRefreshInterval);
          autoRefreshInterval = null;
        }
      }
    }

    // 显示日志
    function showLogs(buildId) {
      const logElement = document.getElementById(`logs-${buildId}`);

      if (logElement.classList.contains('show')) {
        logElement.classList.remove('show');
        return;
      }

      // 模拟日志内容
      const logs = [
        `[${formatTime(new Date(Date.now() - 10000))}] 开始构建任务...`,
        `[${formatTime(new Date(Date.now() - 8000))}] 下载基础镜像...`,
        `[${formatTime(new Date(Date.now() - 6000))}] 安装依赖包...`,
        `[${formatTime(new Date(Date.now() - 4000))}] 编译项目模块...`,
        `[${formatTime(new Date(Date.now() - 2000))}] 打包ISO镜像...`,
        `[${formatTime(new Date())}] 构建进行中...`
      ];

      logElement.innerHTML = logs.map(log => `<div class="log-line log-info">${log}</div>`).join('');
      logElement.classList.add('show');
    }

    // 取消构建
    function cancelBuild(buildId) {
      if (confirm('确定要取消这个构建任务吗？')) {
        const build = builds.find(b => b.id === buildId);
        if (build) {
          build.status = 'failed';
          build.progress = 0;
        }
        renderBuilds();
      }
    }

    // 重新构建
    function retryBuild(buildId) {
      const build = builds.find(b => b.id === buildId);
      if (build) {
        build.status = 'queued';
        build.progress = 0;
        build.startTime = null;
      }
      renderBuilds();
    }

    // 下载ISO
    function downloadISO(buildId) {
      const build = builds.find(b => b.id === buildId);
      if (build) {
        alert(`开始下载 ${build.project} ${build.version} ISO镜像...`);
      }
    }

    // 页面加载完成后执行
    document.addEventListener('DOMContentLoaded', function() {
      // 检查登录状态
      if (sessionStorage.getItem('isLoggedIn') !== 'true') {
        window.location.href = 'login.html';
        return;
      }

      renderBuilds();
    });
  </script>
</body>
</html>
