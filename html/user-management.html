<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>用户管理 - 自动打包平台</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background: #f5f7fa;
      color: #333;
    }

    .header {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      padding: 1rem 2rem;
      box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    }

    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      max-width: 1400px;
      margin: 0 auto;
    }

    .header-title {
      display: flex;
      align-items: center;
      gap: 12px;
    }

    .header-title h1 {
      font-size: 24px;
      font-weight: 600;
    }

    .back-btn {
      background: rgba(255,255,255,0.2);
      border: 1px solid rgba(255,255,255,0.3);
      color: white;
      padding: 8px 16px;
      border-radius: 6px;
      cursor: pointer;
      transition: all 0.3s ease;
      text-decoration: none;
    }

    .back-btn:hover {
      background: rgba(255,255,255,0.3);
    }

    .container {
      max-width: 1400px;
      margin: 0 auto;
      padding: 2rem;
    }

    .controls-section {
      background: white;
      border-radius: 12px;
      padding: 1.5rem;
      margin-bottom: 2rem;
      box-shadow: 0 2px 10px rgba(0,0,0,0.05);
      border: 1px solid #e1e5e9;
    }

    .controls-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      gap: 1rem;
      flex-wrap: wrap;
    }

    .search-group {
      display: flex;
      align-items: center;
      gap: 1rem;
    }

    .search-input {
      padding: 8px 12px;
      border: 1px solid #e1e5e9;
      border-radius: 6px;
      width: 300px;
      font-size: 14px;
    }

    .filter-select {
      padding: 8px 12px;
      border: 1px solid #e1e5e9;
      border-radius: 6px;
      background: white;
      font-size: 14px;
    }

    .add-user-btn {
      background: linear-gradient(135deg, #667eea, #764ba2);
      color: white;
      border: none;
      padding: 10px 20px;
      border-radius: 6px;
      cursor: pointer;
      font-size: 14px;
      font-weight: 500;
      transition: all 0.3s ease;
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .add-user-btn:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
    }

    .users-table {
      background: white;
      border-radius: 12px;
      overflow: hidden;
      box-shadow: 0 2px 10px rgba(0,0,0,0.05);
      border: 1px solid #e1e5e9;
    }

    table {
      width: 100%;
      border-collapse: collapse;
    }

    th {
      background: #f8f9fa;
      color: #333;
      padding: 1rem;
      text-align: left;
      font-weight: 600;
      font-size: 14px;
      border-bottom: 1px solid #e1e5e9;
    }

    td {
      padding: 1rem;
      border-bottom: 1px solid #f0f0f0;
      vertical-align: middle;
    }

    tr:hover {
      background: #f8f9fa;
    }

    .user-avatar {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background: linear-gradient(135deg, #667eea, #764ba2);
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-weight: 600;
      font-size: 16px;
    }

    .user-info {
      display: flex;
      align-items: center;
      gap: 12px;
    }

    .user-details h4 {
      font-size: 14px;
      font-weight: 600;
      margin-bottom: 2px;
    }

    .user-details p {
      font-size: 12px;
      color: #666;
    }

    .user-status {
      padding: 4px 8px;
      border-radius: 12px;
      font-size: 12px;
      font-weight: 500;
    }

    .status-active {
      background: #e8f5e8;
      color: #2e7d32;
    }

    .status-inactive {
      background: #ffebee;
      color: #c62828;
    }

    .status-pending {
      background: #fff3e0;
      color: #f57c00;
    }

    .user-role {
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 12px;
      font-weight: 500;
      background: #f0f4ff;
      color: #667eea;
    }

    .actions-group {
      display: flex;
      gap: 8px;
    }

    .action-btn {
      padding: 6px 12px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 12px;
      font-weight: 500;
      transition: all 0.3s ease;
    }

    .btn-edit {
      background: #fff3e0;
      color: #f57c00;
    }

    .btn-delete {
      background: #ffebee;
      color: #c62828;
    }

    .btn-reset {
      background: #e3f2fd;
      color: #1976d2;
    }

    .action-btn:hover {
      transform: translateY(-1px);
      box-shadow: 0 2px 8px rgba(0,0,0,0.15);
    }

    .modal {
      display: none;
      position: fixed;
      z-index: 1000;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      background: rgba(0,0,0,0.5);
      backdrop-filter: blur(5px);
    }

    .modal-content {
      background: white;
      margin: 5% auto;
      padding: 2rem;
      border-radius: 12px;
      width: 90%;
      max-width: 500px;
      box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    }

    .modal-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 1.5rem;
      padding-bottom: 1rem;
      border-bottom: 1px solid #e1e5e9;
    }

    .modal-title {
      font-size: 20px;
      font-weight: 600;
      color: #333;
    }

    .close-btn {
      background: none;
      border: none;
      font-size: 24px;
      cursor: pointer;
      color: #666;
      padding: 0;
      width: 30px;
      height: 30px;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .form-group {
      margin-bottom: 1rem;
    }

    .form-group label {
      display: block;
      margin-bottom: 6px;
      font-weight: 500;
      color: #333;
      font-size: 14px;
    }

    .form-group input,
    .form-group select {
      width: 100%;
      padding: 10px 12px;
      border: 1px solid #e1e5e9;
      border-radius: 6px;
      font-size: 14px;
      transition: border-color 0.3s ease;
    }

    .form-group input:focus,
    .form-group select:focus {
      outline: none;
      border-color: #667eea;
      box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }

    .form-row {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 1rem;
    }

    .modal-actions {
      display: flex;
      justify-content: flex-end;
      gap: 12px;
      margin-top: 2rem;
      padding-top: 1rem;
      border-top: 1px solid #e1e5e9;
    }

    .btn-cancel {
      background: #f8f9fa;
      color: #666;
      border: 1px solid #e1e5e9;
      padding: 10px 20px;
      border-radius: 6px;
      cursor: pointer;
      font-size: 14px;
    }

    .btn-save {
      background: linear-gradient(135deg, #667eea, #764ba2);
      color: white;
      border: none;
      padding: 10px 20px;
      border-radius: 6px;
      cursor: pointer;
      font-size: 14px;
      font-weight: 500;
    }

    .empty-state {
      text-align: center;
      padding: 3rem;
      color: #666;
    }

    .empty-state-icon {
      font-size: 48px;
      margin-bottom: 1rem;
      opacity: 0.5;
    }

    @media (max-width: 768px) {
      .container {
        padding: 1rem;
      }

      .controls-row {
        flex-direction: column;
        align-items: stretch;
      }

      .search-group {
        justify-content: space-between;
      }

      .search-input {
        width: 100%;
      }

      .users-table {
        overflow-x: auto;
      }

      .form-row {
        grid-template-columns: 1fr;
      }
    }
  </style>
</head>
<body>
  <header class="header">
    <div class="header-content">
      <div class="header-title">
        <span>👥</span>
        <h1>用户管理</h1>
      </div>
      <a href="dashboard.html" class="back-btn">返回仪表板</a>
    </div>
  </header>

  <div class="container">
    <div class="controls-section">
      <div class="controls-row">
        <div class="search-group">
          <input type="text" class="search-input" id="searchInput" placeholder="搜索用户名、邮箱或部门..." onkeyup="searchUsers()">

          <select class="filter-select" id="roleFilter" onchange="filterUsers()">
            <option value="all">全部角色</option>
            <option value="admin">管理员</option>
            <option value="developer">开发者</option>
            <option value="operator">运维人员</option>
            <option value="viewer">查看者</option>
          </select>

          <select class="filter-select" id="statusFilter" onchange="filterUsers()">
            <option value="all">全部状态</option>
            <option value="active">活跃</option>
            <option value="inactive">停用</option>
            <option value="pending">待激活</option>
          </select>
        </div>

        <button class="add-user-btn" onclick="openAddUserModal()">
          <span>➕</span>
          添加用户
        </button>
      </div>
    </div>

    <div class="users-table">
      <table>
        <thead>
          <tr>
            <th>用户</th>
            <th>邮箱</th>
            <th>部门</th>
            <th>角色</th>
            <th>状态</th>
            <th>最后登录</th>
            <th>操作</th>
          </tr>
        </thead>
        <tbody id="usersTableBody">
          <!-- 用户数据将通过JavaScript动态加载 -->
        </tbody>
      </table>
    </div>

    <div class="empty-state" id="emptyState" style="display: none;">
      <div class="empty-state-icon">👤</div>
      <h3>暂无用户</h3>
      <p>当前没有符合条件的用户</p>
    </div>
  </div>

  <!-- 添加/编辑用户模态框 -->
  <div class="modal" id="userModal">
    <div class="modal-content">
      <div class="modal-header">
        <h2 class="modal-title" id="modalTitle">添加用户</h2>
        <button class="close-btn" onclick="closeUserModal()">&times;</button>
      </div>

      <form id="userForm" onsubmit="saveUser(event)">
        <input type="hidden" id="userId" name="userId">

        <div class="form-row">
          <div class="form-group">
            <label for="firstName">姓</label>
            <input type="text" id="firstName" name="firstName" required>
          </div>
          <div class="form-group">
            <label for="lastName">名</label>
            <input type="text" id="lastName" name="lastName" required>
          </div>
        </div>

        <div class="form-group">
          <label for="username">用户名</label>
          <input type="text" id="username" name="username" required>
        </div>

        <div class="form-group">
          <label for="email">邮箱</label>
          <input type="email" id="email" name="email" required>
        </div>

        <div class="form-row">
          <div class="form-group">
            <label for="department">部门</label>
            <select id="department" name="department" required>
              <option value="">请选择部门</option>
              <option value="研发部">研发部</option>
              <option value="运维部">运维部</option>
              <option value="测试部">测试部</option>
              <option value="产品部">产品部</option>
              <option value="其他">其他</option>
            </select>
          </div>
          <div class="form-group">
            <label for="role">角色</label>
            <select id="role" name="role" required>
              <option value="">请选择角色</option>
              <option value="admin">管理员</option>
              <option value="developer">开发者</option>
              <option value="operator">运维人员</option>
              <option value="viewer">查看者</option>
            </select>
          </div>
        </div>

        <div class="form-group">
          <label for="phone">手机号码</label>
          <input type="tel" id="phone" name="phone">
        </div>

        <div class="modal-actions">
          <button type="button" class="btn-cancel" onclick="closeUserModal()">取消</button>
          <button type="submit" class="btn-save">保存</button>
        </div>
      </form>
    </div>
  </div>

  <script>
    // 模拟用户数据
    let users = [
      {
        id: 1,
        firstName: '张',
        lastName: '三',
        username: 'zhangsan',
        email: '<EMAIL>',
        department: '研发部',
        role: 'admin',
        status: 'active',
        phone: '13800138001',
        lastLogin: new Date(Date.now() - 2 * 60 * 60 * 1000),
        createdAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
      },
      {
        id: 2,
        firstName: '李',
        lastName: '四',
        username: 'lisi',
        email: '<EMAIL>',
        department: '运维部',
        role: 'operator',
        status: 'active',
        phone: '13800138002',
        lastLogin: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
        createdAt: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000)
      },
      {
        id: 3,
        firstName: '王',
        lastName: '五',
        username: 'wangwu',
        email: '<EMAIL>',
        department: '测试部',
        role: 'developer',
        status: 'inactive',
        phone: '13800138003',
        lastLogin: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
        createdAt: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000)
      },
      {
        id: 4,
        firstName: '赵',
        lastName: '六',
        username: 'zhaoliu',
        email: '<EMAIL>',
        department: '产品部',
        role: 'viewer',
        status: 'pending',
        phone: '13800138004',
        lastLogin: null,
        createdAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000)
      }
    ];

    let editingUserId = null;

    // 渲染用户列表
    function renderUsers() {
      const tbody = document.getElementById('usersTableBody');
      const emptyState = document.getElementById('emptyState');

      const searchTerm = document.getElementById('searchInput').value.toLowerCase();
      const roleFilter = document.getElementById('roleFilter').value;
      const statusFilter = document.getElementById('statusFilter').value;

      let filteredUsers = users.filter(user => {
        const matchesSearch = !searchTerm ||
          user.username.toLowerCase().includes(searchTerm) ||
          user.email.toLowerCase().includes(searchTerm) ||
          user.department.toLowerCase().includes(searchTerm) ||
          `${user.firstName}${user.lastName}`.toLowerCase().includes(searchTerm);

        const matchesRole = roleFilter === 'all' || user.role === roleFilter;
        const matchesStatus = statusFilter === 'all' || user.status === statusFilter;

        return matchesSearch && matchesRole && matchesStatus;
      });

      if (filteredUsers.length === 0) {
        tbody.innerHTML = '';
        emptyState.style.display = 'block';
        return;
      }

      emptyState.style.display = 'none';

      tbody.innerHTML = filteredUsers.map(user => `
        <tr>
          <td>
            <div class="user-info">
              <div class="user-avatar">${user.firstName}</div>
              <div class="user-details">
                <h4>${user.firstName}${user.lastName}</h4>
                <p>@${user.username}</p>
              </div>
            </div>
          </td>
          <td>${user.email}</td>
          <td>${user.department}</td>
          <td><span class="user-role">${getRoleText(user.role)}</span></td>
          <td><span class="user-status status-${user.status}">${getStatusText(user.status)}</span></td>
          <td>${user.lastLogin ? formatTime(user.lastLogin) : '从未登录'}</td>
          <td>
            <div class="actions-group">
              <button class="action-btn btn-edit" onclick="editUser(${user.id})">编辑</button>
              <button class="action-btn btn-reset" onclick="resetPassword(${user.id})">重置密码</button>
              <button class="action-btn btn-delete" onclick="deleteUser(${user.id})">删除</button>
            </div>
          </td>
        </tr>
      `).join('');
    }

    // 获取角色文本
    function getRoleText(role) {
      const roleTexts = {
        admin: '管理员',
        developer: '开发者',
        operator: '运维人员',
        viewer: '查看者'
      };
      return roleTexts[role] || role;
    }

    // 获取状态文本
    function getStatusText(status) {
      const statusTexts = {
        active: '活跃',
        inactive: '停用',
        pending: '待激活'
      };
      return statusTexts[status] || status;
    }

    // 格式化时间
    function formatTime(date) {
      const now = new Date();
      const diff = now - date;
      const days = Math.floor(diff / (1000 * 60 * 60 * 24));

      if (days === 0) {
        const hours = Math.floor(diff / (1000 * 60 * 60));
        if (hours === 0) {
          const minutes = Math.floor(diff / (1000 * 60));
          return `${minutes}分钟前`;
        }
        return `${hours}小时前`;
      } else if (days === 1) {
        return '昨天';
      } else if (days < 7) {
        return `${days}天前`;
      } else {
        return date.toLocaleDateString('zh-CN');
      }
    }

    // 搜索用户
    function searchUsers() {
      renderUsers();
    }

    // 筛选用户
    function filterUsers() {
      renderUsers();
    }

    // 打开添加用户模态框
    function openAddUserModal() {
      editingUserId = null;
      document.getElementById('modalTitle').textContent = '添加用户';
      document.getElementById('userForm').reset();
      document.getElementById('userModal').style.display = 'block';
    }

    // 编辑用户
    function editUser(userId) {
      const user = users.find(u => u.id === userId);
      if (!user) return;

      editingUserId = userId;
      document.getElementById('modalTitle').textContent = '编辑用户';

      // 填充表单
      document.getElementById('userId').value = user.id;
      document.getElementById('firstName').value = user.firstName;
      document.getElementById('lastName').value = user.lastName;
      document.getElementById('username').value = user.username;
      document.getElementById('email').value = user.email;
      document.getElementById('department').value = user.department;
      document.getElementById('role').value = user.role;
      document.getElementById('phone').value = user.phone || '';

      document.getElementById('userModal').style.display = 'block';
    }

    // 关闭用户模态框
    function closeUserModal() {
      document.getElementById('userModal').style.display = 'none';
      editingUserId = null;
    }

    // 保存用户
    function saveUser(event) {
      event.preventDefault();

      const formData = new FormData(event.target);
      const userData = Object.fromEntries(formData);

      if (editingUserId) {
        // 编辑现有用户
        const userIndex = users.findIndex(u => u.id === editingUserId);
        if (userIndex !== -1) {
          users[userIndex] = {
            ...users[userIndex],
            ...userData,
            id: editingUserId
          };
        }
      } else {
        // 添加新用户
        const newUser = {
          ...userData,
          id: Math.max(...users.map(u => u.id)) + 1,
          status: 'pending',
          lastLogin: null,
          createdAt: new Date()
        };
        users.push(newUser);
      }

      renderUsers();
      closeUserModal();

      const action = editingUserId ? '更新' : '添加';
      alert(`用户${action}成功！`);
    }

    // 重置密码
    function resetPassword(userId) {
      const user = users.find(u => u.id === userId);
      if (!user) return;

      if (confirm(`确定要重置用户 ${user.firstName}${user.lastName} 的密码吗？`)) {
        alert(`已为用户 ${user.firstName}${user.lastName} 重置密码，新密码已发送到邮箱 ${user.email}`);
      }
    }

    // 删除用户
    function deleteUser(userId) {
      const user = users.find(u => u.id === userId);
      if (!user) return;

      if (confirm(`确定要删除用户 ${user.firstName}${user.lastName} 吗？此操作不可撤销。`)) {
        users = users.filter(u => u.id !== userId);
        renderUsers();
        alert(`用户 ${user.firstName}${user.lastName} 已删除`);
      }
    }

    // 点击模态框外部关闭
    window.onclick = function(event) {
      const modal = document.getElementById('userModal');
      if (event.target === modal) {
        closeUserModal();
      }
    }

    // 页面加载完成后执行
    document.addEventListener('DOMContentLoaded', function() {
      // 检查登录状态
      if (sessionStorage.getItem('isLoggedIn') !== 'true') {
        window.location.href = 'login.html';
        return;
      }

      renderUsers();
    });
  </script>
</body>
</html>
