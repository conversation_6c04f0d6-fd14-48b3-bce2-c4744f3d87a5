<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>注册 - 自动打包平台</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
      overflow: hidden;
    }

    /* 背景动画效果 */
    body::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
      animation: float 20s ease-in-out infinite;
    }

    @keyframes float {
      0%, 100% { transform: translateY(0px) rotate(0deg); }
      50% { transform: translateY(-20px) rotate(1deg); }
    }

    .register-container {
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(10px);
      border-radius: 20px;
      box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
      padding: 40px;
      width: 100%;
      max-width: 480px;
      position: relative;
      z-index: 1;
      border: 1px solid rgba(255, 255, 255, 0.2);
      max-height: 90vh;
      overflow-y: auto;
    }

    .logo-section {
      text-align: center;
      margin-bottom: 30px;
    }

    .logo {
      font-size: 48px;
      margin-bottom: 10px;
      background: linear-gradient(135deg, #667eea, #764ba2);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    .platform-title {
      font-size: 24px;
      font-weight: 600;
      color: #333;
      margin-bottom: 8px;
    }

    .platform-subtitle {
      font-size: 14px;
      color: #666;
      margin-bottom: 20px;
    }

    .form-row {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 15px;
      margin-bottom: 20px;
    }

    .form-group {
      margin-bottom: 20px;
      position: relative;
    }

    .form-group.full-width {
      grid-column: 1 / -1;
    }

    .form-group label {
      display: block;
      margin-bottom: 8px;
      font-weight: 500;
      color: #333;
      font-size: 14px;
    }

    .form-group input,
    .form-group select {
      width: 100%;
      padding: 12px 16px;
      border: 2px solid #e1e5e9;
      border-radius: 10px;
      font-size: 16px;
      transition: all 0.3s ease;
      background: #f8f9fa;
    }

    .form-group input:focus,
    .form-group select:focus {
      outline: none;
      border-color: #667eea;
      background: white;
      box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }

    .form-group .input-icon {
      position: absolute;
      right: 12px;
      top: 38px;
      color: #999;
      font-size: 18px;
    }

    .password-strength {
      margin-top: 8px;
      font-size: 12px;
    }

    .strength-bar {
      height: 4px;
      background: #e1e5e9;
      border-radius: 2px;
      margin: 4px 0;
      overflow: hidden;
    }

    .strength-fill {
      height: 100%;
      transition: all 0.3s ease;
      border-radius: 2px;
    }

    .strength-weak { background: #f44336; width: 25%; }
    .strength-fair { background: #ff9800; width: 50%; }
    .strength-good { background: #2196f3; width: 75%; }
    .strength-strong { background: #4caf50; width: 100%; }

    .terms-section {
      margin: 20px 0;
      font-size: 14px;
    }

    .terms-checkbox {
      display: flex;
      align-items: flex-start;
      gap: 8px;
      margin-bottom: 15px;
    }

    .terms-checkbox input[type="checkbox"] {
      width: auto;
      margin: 0;
      margin-top: 2px;
    }

    .terms-text {
      color: #666;
      line-height: 1.4;
    }

    .terms-link {
      color: #667eea;
      text-decoration: none;
    }

    .terms-link:hover {
      color: #764ba2;
      text-decoration: underline;
    }

    .register-btn {
      width: 100%;
      padding: 14px;
      background: linear-gradient(135deg, #667eea, #764ba2);
      color: white;
      border: none;
      border-radius: 10px;
      font-size: 16px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;
    }

    .register-btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
    }

    .register-btn:active {
      transform: translateY(0);
    }

    .register-btn:disabled {
      opacity: 0.6;
      cursor: not-allowed;
      transform: none;
      box-shadow: none;
    }

    .divider {
      text-align: center;
      margin: 25px 0;
      position: relative;
      color: #999;
      font-size: 14px;
    }

    .divider::before {
      content: '';
      position: absolute;
      top: 50%;
      left: 0;
      right: 0;
      height: 1px;
      background: #e1e5e9;
      z-index: 1;
    }

    .divider span {
      background: rgba(255, 255, 255, 0.95);
      padding: 0 15px;
      position: relative;
      z-index: 2;
    }

    .login-link {
      text-align: center;
      margin-top: 20px;
      font-size: 14px;
      color: #666;
    }

    .login-link a {
      color: #667eea;
      text-decoration: none;
      font-weight: 600;
      transition: color 0.3s ease;
    }

    .login-link a:hover {
      color: #764ba2;
    }

    .error-message,
    .success-message {
      padding: 10px;
      border-radius: 8px;
      margin-bottom: 20px;
      font-size: 14px;
      display: none;
    }

    .error-message {
      background: #fee;
      color: #c33;
      border: 1px solid #fcc;
    }

    .success-message {
      background: #efe;
      color: #2c5;
      border: 1px solid #cfc;
    }

    .floating-shapes {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      overflow: hidden;
      z-index: 0;
    }

    .shape {
      position: absolute;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 50%;
      animation: float-shapes 15s infinite linear;
    }

    .shape:nth-child(1) {
      width: 80px;
      height: 80px;
      top: 20%;
      left: 10%;
      animation-delay: 0s;
    }

    .shape:nth-child(2) {
      width: 120px;
      height: 120px;
      top: 60%;
      right: 10%;
      animation-delay: 5s;
    }

    .shape:nth-child(3) {
      width: 60px;
      height: 60px;
      bottom: 20%;
      left: 20%;
      animation-delay: 10s;
    }

    @keyframes float-shapes {
      0% { transform: translateY(0px) rotate(0deg); opacity: 0.7; }
      50% { transform: translateY(-100px) rotate(180deg); opacity: 0.3; }
      100% { transform: translateY(0px) rotate(360deg); opacity: 0.7; }
    }

    @media (max-width: 480px) {
      .register-container {
        margin: 20px;
        padding: 30px 25px;
      }

      .form-row {
        grid-template-columns: 1fr;
      }

      .platform-title {
        font-size: 20px;
      }

      .logo {
        font-size: 40px;
      }
    }
  </style>
</head>
<body>
  <div class="floating-shapes">
    <div class="shape"></div>
    <div class="shape"></div>
    <div class="shape"></div>
  </div>

  <div class="register-container">
    <div class="logo-section">
      <div class="logo">📦</div>
      <h1 class="platform-title">创建账户</h1>
      <p class="platform-subtitle">加入自动打包平台，开始您的项目管理之旅</p>
    </div>

    <div class="error-message" id="errorMessage"></div>
    <div class="success-message" id="successMessage"></div>

    <form id="registerForm" onsubmit="handleRegister(event)">
      <div class="form-row">
        <div class="form-group">
          <label for="firstName">姓</label>
          <input type="text" id="firstName" name="firstName" required placeholder="请输入姓">
        </div>
        <div class="form-group">
          <label for="lastName">名</label>
          <input type="text" id="lastName" name="lastName" required placeholder="请输入名">
        </div>
      </div>

      <div class="form-group">
        <label for="username">用户名</label>
        <input type="text" id="username" name="username" required placeholder="请输入用户名" onblur="checkUsername()">
        <span class="input-icon">👤</span>
      </div>

      <div class="form-group">
        <label for="email">邮箱地址</label>
        <input type="email" id="email" name="email" required placeholder="请输入邮箱地址">
        <span class="input-icon">📧</span>
      </div>

      <div class="form-row">
        <div class="form-group">
          <label for="phone">手机号码</label>
          <input type="tel" id="phone" name="phone" placeholder="请输入手机号码">
          <span class="input-icon">📱</span>
        </div>
        <div class="form-group">
          <label for="department">部门</label>
          <select id="department" name="department" required>
            <option value="">请选择部门</option>
            <option value="development">研发部</option>
            <option value="operations">运维部</option>
            <option value="testing">测试部</option>
            <option value="product">产品部</option>
            <option value="other">其他</option>
          </select>
        </div>
      </div>

      <div class="form-group">
        <label for="password">密码</label>
        <input type="password" id="password" name="password" required placeholder="请输入密码" oninput="checkPasswordStrength()">
        <span class="input-icon">🔒</span>
        <div class="password-strength">
          <div class="strength-bar">
            <div class="strength-fill" id="strengthFill"></div>
          </div>
          <div id="strengthText">密码强度：请输入密码</div>
        </div>
      </div>

      <div class="form-group">
        <label for="confirmPassword">确认密码</label>
        <input type="password" id="confirmPassword" name="confirmPassword" required placeholder="请再次输入密码" oninput="checkPasswordMatch()">
        <span class="input-icon">🔒</span>
      </div>

      <div class="terms-section">
        <label class="terms-checkbox">
          <input type="checkbox" id="agreeTerms" name="agreeTerms" required>
          <span class="terms-text">
            我已阅读并同意 <a href="#" class="terms-link">用户协议</a> 和 <a href="#" class="terms-link">隐私政策</a>
          </span>
        </label>

        <label class="terms-checkbox">
          <input type="checkbox" id="agreeNewsletter" name="agreeNewsletter">
          <span class="terms-text">
            我希望接收产品更新和技术资讯（可选）
          </span>
        </label>
      </div>

      <button type="submit" class="register-btn" id="registerBtn">
        创建账户
      </button>
    </form>

    <div class="divider">
      <span>或</span>
    </div>

    <div class="login-link">
      已有账户？ <a href="login.html">立即登录</a>
    </div>
  </div>

  <script>
    // 检查密码强度
    function checkPasswordStrength() {
      const password = document.getElementById('password').value;
      const strengthFill = document.getElementById('strengthFill');
      const strengthText = document.getElementById('strengthText');

      let strength = 0;
      let strengthLabel = '';

      if (password.length >= 8) strength++;
      if (/[a-z]/.test(password)) strength++;
      if (/[A-Z]/.test(password)) strength++;
      if (/[0-9]/.test(password)) strength++;
      if (/[^A-Za-z0-9]/.test(password)) strength++;

      strengthFill.className = 'strength-fill';

      switch (strength) {
        case 0:
        case 1:
          strengthFill.classList.add('strength-weak');
          strengthLabel = '弱';
          break;
        case 2:
          strengthFill.classList.add('strength-fair');
          strengthLabel = '一般';
          break;
        case 3:
        case 4:
          strengthFill.classList.add('strength-good');
          strengthLabel = '良好';
          break;
        case 5:
          strengthFill.classList.add('strength-strong');
          strengthLabel = '强';
          break;
      }

      strengthText.textContent = `密码强度：${strengthLabel}`;
    }

    // 检查密码匹配
    function checkPasswordMatch() {
      const password = document.getElementById('password').value;
      const confirmPassword = document.getElementById('confirmPassword').value;
      const confirmInput = document.getElementById('confirmPassword');

      if (confirmPassword && password !== confirmPassword) {
        confirmInput.style.borderColor = '#f44336';
      } else {
        confirmInput.style.borderColor = '#e1e5e9';
      }
    }

    // 检查用户名是否可用
    function checkUsername() {
      const username = document.getElementById('username').value;
      const usernameInput = document.getElementById('username');

      if (username.length < 3) {
        usernameInput.style.borderColor = '#f44336';
        return;
      }

      // 模拟检查用户名是否已存在
      const existingUsers = ['admin', 'test', 'user'];
      if (existingUsers.includes(username.toLowerCase())) {
        usernameInput.style.borderColor = '#f44336';
        showError('用户名已存在，请选择其他用户名');
      } else {
        usernameInput.style.borderColor = '#4caf50';
      }
    }

    // 处理注册
    function handleRegister(event) {
      event.preventDefault();

      const formData = new FormData(event.target);
      const data = Object.fromEntries(formData);

      // 验证必填字段
      if (!data.firstName || !data.lastName || !data.username || !data.email || !data.password || !data.confirmPassword || !data.department) {
        showError('请填写所有必填字段');
        return;
      }

      // 验证密码匹配
      if (data.password !== data.confirmPassword) {
        showError('两次输入的密码不一致');
        return;
      }

      // 验证密码强度
      if (data.password.length < 8) {
        showError('密码长度至少为8位');
        return;
      }

      // 验证邮箱格式
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(data.email)) {
        showError('请输入有效的邮箱地址');
        return;
      }

      // 验证用户协议
      if (!data.agreeTerms) {
        showError('请同意用户协议和隐私政策');
        return;
      }

      // 模拟注册过程
      const registerBtn = document.getElementById('registerBtn');
      registerBtn.disabled = true;
      registerBtn.textContent = '注册中...';

      setTimeout(() => {
        // 模拟注册成功
        showSuccess('注册成功！正在跳转到登录页面...');

        setTimeout(() => {
          window.location.href = 'login.html';
        }, 2000);
      }, 1500);
    }

    // 显示错误信息
    function showError(message) {
      const errorDiv = document.getElementById('errorMessage');
      const successDiv = document.getElementById('successMessage');

      successDiv.style.display = 'none';
      errorDiv.textContent = message;
      errorDiv.style.display = 'block';

      setTimeout(() => {
        errorDiv.style.display = 'none';
      }, 5000);
    }

    // 显示成功信息
    function showSuccess(message) {
      const errorDiv = document.getElementById('errorMessage');
      const successDiv = document.getElementById('successMessage');

      errorDiv.style.display = 'none';
      successDiv.textContent = message;
      successDiv.style.display = 'block';
    }

    // 页面加载完成后执行
    document.addEventListener('DOMContentLoaded', function() {
      // 如果已经登录，跳转到仪表板
      if (sessionStorage.getItem('isLoggedIn') === 'true') {
        window.location.href = 'dashboard.html';
      }
    });
  </script>
</body>
</html>
