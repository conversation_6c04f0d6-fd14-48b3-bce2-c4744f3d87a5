<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <title>项目总览</title>
  <style>
    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }
    .header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
    }
    .action-link {
      color: #2196F3;
      text-decoration: none;
      margin: 0 5px;
    }
    table {
      width: 100%;
      border-collapse: collapse;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
      border-radius: 5px;
      overflow: hidden;
    }
    th {
      background-color: #3f51b5;
      color: white;
      padding: 12px 15px;
      text-align: left;
      font-weight: bold;
      font-size: 14px;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      border-bottom: 2px solid #ddd;
    }
    td {
      padding: 10px 15px;
      text-align: left;
      border-bottom: 1px solid #ddd;
    }
    tr:hover {
      background-color: #f5f5f5;
    }
    .search-bar {
      display: flex;
      gap: 10px;
      margin-bottom: 20px;
    }
    .search-bar input {
      padding: 8px;
      width: 300px;
    }
    .search-bar button {
      padding: 8px 15px;
      background-color: #4CAF50;
      color: white;
      border: none;
      cursor: pointer;
    }
    .action-buttons {
      margin-bottom: 20px;
    }
    .action-buttons button {
      padding: 8px 15px;
      margin-right: 10px;
      cursor: pointer;
    }
    .add-button {
      background-color: #4CAF50;
      color: white;
      border: none;
      border-radius: 4px;
    }
    .back-btn {
      padding: 8px 15px;
      background-color: #2196F3;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
    }
    .section-actions {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>📦 超融合打包的项目列表</h1>
      <div>
        <button onclick="location.href='/html/base_iso.html'" class="back-btn">基础镜像管理</button>
      </div>
    </div>
    
    <div class="section-actions">
      <div class="search-bar">
        <input type="text" id="searchInput" placeholder="搜索项目名称或描述...">
        <button onclick="searchProjects()">搜索</button>
      </div>
      
      <div class="action-buttons">
        <button class="add-button" onclick="location.href='#'">添加新项目</button>
      </div>
    </div>
    <table>
      <thead>
        <tr>
          <th>项目名称</th>
          <th>描述</th>
          <th>iso制作历史</th>
          <th>模块管理</th>
          <th>制作ISO</th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td>超融合3.2</td>
          <td>云平台openstack</td>
          <td><a href="/html/project_iso_history.html" class="action-link">进入</a></td>
          <td><a href="/html/module_list.html" class="action-link">进入</a></td>
          <td><a href="/html/project_iso.html" class="action-link">进入</a></td>
        </tr>
        <tr>
          <td>云桌面 2.0</td>
          <td>云平台 云桌面 openstack</td>
          <td><a href="/html/project_iso_history.html" class="action-link">进入</a></td>
          <td><a href="/html/module_list.html" class="action-link">进入</a></td>
          <td><a href="/html/project_iso.html" class="action-link">进入</a></td>
        </tr>
      </tbody>
    </table>
  </div>
  
  <script>
    // 搜索项目
    function searchProjects() {
      const searchValue = document.getElementById('searchInput').value.toLowerCase();
      const rows = document.querySelectorAll('tbody tr');
      
      rows.forEach(row => {
        const projectName = row.querySelector('td:first-child').textContent.toLowerCase();
        const description = row.querySelector('td:nth-child(2)').textContent.toLowerCase();
        
        if (projectName.includes(searchValue) || description.includes(searchValue)) {
          row.style.display = '';
        } else {
          row.style.display = 'none';
        }
      });
    }
  </script>
</body> 
</html>