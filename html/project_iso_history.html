<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>📜 ISO 制作历史 - 天华星航 云平台</title>
  <style>
    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }
    .header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
    }
    .back-btn {
      padding: 8px 15px;
      background-color: #2196F3;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
    }
    /* 手风琴样式 */
    .details-row {
      display: none;
      background-color: #f9f9f9;
    }
    .details-content {
      padding: 15px;
      border-radius: 4px;
    }
    .details-grid {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      gap: 12px;
      margin: 15px 0;
    }
    .details-item {
      padding: 8px 0;
    }
    .details-actions {
      margin-top: 15px;
      display: flex;
      gap: 10px;
    }
    .parent-row {
      cursor: pointer;
      transition: background-color 0.2s;
    }
    .parent-row:hover {
      background-color: #eaf0fd;
    }
    .expand-btn {
      background-color: #3f51b5;
      color: white;
      border: none;
      border-radius: 4px;
      padding: 5px 10px;
      cursor: pointer;
      display: inline-flex;
      align-items: center;
    }
    .expand-icon {
      font-size: 10px;
      margin-left: 5px;
      transition: transform 0.3s;
    }
    .search-bar {
      display: flex;
      gap: 10px;
      margin-bottom: 20px;
    }
    .search-bar input {
      padding: 8px;
      width: 300px;
    }
    .search-bar button {
      padding: 8px 15px;
      background-color: #4CAF50;
      color: white;
      border: none;
      cursor: pointer;
    }
    .filter-controls {
      display: flex;
      gap: 15px;
      margin-bottom: 20px;
      align-items: center;
    }
    .filter-select {
      padding: 8px;
      border-radius: 4px;
      border: 1px solid #ddd;
    }
    table {
      width: 100%;
      border-collapse: collapse;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
      border-radius: 5px;
      overflow: hidden;
    }
    th {
      background-color: #3f51b5;
      color: white;
      padding: 12px 15px;
      text-align: left;
      font-weight: bold;
      font-size: 14px;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      border-bottom: 2px solid #ddd;
    }
    td {
      padding: 10px 15px;
      text-align: left;
      border-bottom: 1px solid #ddd;
    }
    tr:hover {
      background-color: #f5f5f5;
    }
    .action-link {
      color: #2196F3;
      text-decoration: none;
      margin: 0 5px;
    }
    .status-success {
      color: #4CAF50;
      font-weight: bold;
    }
    .status-failed {
      color: #F44336;
      font-weight: bold;
    }
    .table-responsive {
      overflow-x: auto;
    }
    .download-btn {
      padding: 5px 10px;
      background-color: #2196F3;
      color: white;
      border: none;
      border-radius: 4px;
      text-decoration: none;
      font-size: 12px;
    }
    .log-btn {
      padding: 5px 10px;
      background-color: #FF9800;
      color: white;
      border: none;
      border-radius: 4px;
      text-decoration: none;
      font-size: 12px;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>📜 ISO 制作历史 - 超融合3.2</h1>
      <div>
        <button onclick="location.href='/html/project.html'" class="back-btn">返回项目列表</button>
      </div>
    </div>

    <div class="search-bar">
      <input type="text" id="searchInput" placeholder="搜索版本、描述、构建人...">
      <button onclick="searchHistory()">搜索</button>
    </div>

    <div class="filter-controls">
      <label>筛选状态:</label>
      <select class="filter-select" id="statusFilter" onchange="filterHistory()">
        <option value="all">全部</option>
        <option value="success">成功</option>
        <option value="failed">失败</option>
      </select>

      <label>操作系统:</label>
      <select class="filter-select" id="osFilter" onchange="filterHistory()">
        <option value="all">全部</option>
        <option value="Anolis">Anolis</option>
        <option value="Kylin">Kylin</option>
      </select>

      <label>CPU架构:</label>
      <select class="filter-select" id="archFilter" onchange="filterHistory()">
        <option value="all">全部</option>
        <option value="x86">x86</option>
        <option value="ARM">ARM</option>
      </select>
    </div>

    <div class="table-responsive">
      <table class="main-table">
        <thead>
          <tr>
            <th>项目名称</th>
            <th>描述</th>
            <th>构建版本</th>
            <th>操作系统</th>
            <th>CPU架构</th>
            <th>构建时间</th>
            <th>状态</th>
            <th>操作</th>
          </tr>
        </thead>
        <tbody id="historyTableBody">
          <tr class="parent-row" onclick="toggleDetails('details-1')">
            <td>超融合3.2</td>
            <td>云平台openstack</td>
            <td>v3.2</td>
            <td>Anolis</td>
            <td>ARM</td>
            <td>2025-05-22 10:03</td>
            <td class="status-success">✅ 成功</td>
            <td>
              <button class="expand-btn" onclick="event.stopPropagation(); toggleDetails('details-1')" aria-label="展开超融合3.2详情">
                详情 <span class="expand-icon">▼</span>
              </button>
            </td>
          </tr>
          <tr class="details-row" id="details-1">
            <td colspan="8">
              <div class="details-content">
                <h4>详细信息：超融合3.2 - v3.2</h4>
                <div class="details-grid">
                  <div class="details-item">
                    <strong>包含模块：</strong> frontend, api
                  </div>
                  <div class="details-item">
                    <strong>基础镜像：</strong> anolis-8.8-arm.iso
                  </div>
                  <div class="details-item">
                    <strong>构建分支：</strong> arm
                  </div>
                  <div class="details-item">
                    <strong>Commit ID：</strong> abcdef123456
                  </div>
                  <div class="details-item">
                    <strong>构建人：</strong> alice
                  </div>
                </div>
                <div class="details-actions">
                  <a href="/logs/iso/v3.2" class="log-btn">查看日志</a>
                  <a href="/isos/iso-v3.2.iso" class="download-btn">下载ISO</a>
                </div>
              </div>
            </td>
          </tr>
          
          <tr class="parent-row" onclick="toggleDetails('details-2')">
            <td>云桌面2.0</td>
            <td>云平台 云桌面 openstack</td>
            <td>v2.0.22</td>
            <td>Kylin V10</td>
            <td>x86</td>
            <td>2025-04-15 16:42</td>
            <td class="status-failed">❌ 失败</td>
            <td>
              <button class="expand-btn" onclick="event.stopPropagation(); toggleDetails('details-2')" aria-label="展开云桌面2.0详情">
                详情 <span class="expand-icon">▼</span>
              </button>
            </td>
          </tr>
          <tr class="details-row" id="details-2">
            <td colspan="8">
              <div class="details-content">
                <h4>详细信息：云桌面2.0 - v2.0.22</h4>
                <div class="details-grid">
                  <div class="details-item">
                    <strong>包含模块：</strong> frontend, api, scheduler
                  </div>
                  <div class="details-item">
                    <strong>基础镜像：</strong> kylin-v10-x86.iso
                  </div>
                  <div class="details-item">
                    <strong>构建分支：</strong> x86
                  </div>
                  <div class="details-item">
                    <strong>Commit ID：</strong> 998877aabbcc
                  </div>
                  <div class="details-item">
                    <strong>构建人：</strong> bob
                  </div>
                </div>
                <div class="details-actions">
                  <a href="/logs/iso/v2.0.22" class="log-btn">查看日志</a>
                </div>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>

  <script>
    // 切换详情展示
    function toggleDetails(detailsId) {
      const detailsRow = document.getElementById(detailsId);
      const parentRow = detailsRow.previousElementSibling;
      const expandIcon = parentRow.querySelector('.expand-icon');
      
      if (detailsRow.style.display === 'table-row') {
        detailsRow.style.display = 'none';
        expandIcon.style.transform = 'rotate(0deg)';
      } else {
        detailsRow.style.display = 'table-row';
        expandIcon.style.transform = 'rotate(180deg)';
      }
    }
    
    // 搜索ISO历史记录
    function searchHistory() {
      const searchValue = document.getElementById('searchInput').value.toLowerCase();
      const rows = document.querySelectorAll('#historyTableBody tr.parent-row');
      
      rows.forEach(row => {
        const version = row.querySelector('td:nth-child(3)').textContent.toLowerCase();
        const desc = row.querySelector('td:nth-child(2)').textContent.toLowerCase();
        const projectName = row.querySelector('td:first-child').textContent.toLowerCase();
        
        // 在详情中也搜索构建人等信息
        const detailsId = row.nextElementSibling.id;
        const detailsContent = document.getElementById(detailsId).querySelector('.details-content').innerText.toLowerCase();
        
        if (version.includes(searchValue) || 
            desc.includes(searchValue) || 
            projectName.includes(searchValue) ||
            detailsContent.includes(searchValue)) {
          row.style.display = '';
        } else {
          row.style.display = 'none';
        }
        
        // 总是隐藏详情行，除非被展开
        row.nextElementSibling.style.display = 'none';
      });
    }

    // 筛选ISO历史记录
    function filterHistory() {
      const statusFilter = document.getElementById('statusFilter').value;
      const osFilter = document.getElementById('osFilter').value;
      const archFilter = document.getElementById('archFilter').value;
      
      const rows = document.querySelectorAll('#historyTableBody tr.parent-row');
      
      rows.forEach(row => {
        const status = row.querySelector('td:nth-child(7)').textContent;
        const os = row.querySelector('td:nth-child(4)').textContent;
        const arch = row.querySelector('td:nth-child(5)').textContent;
        
        let statusMatch = statusFilter === 'all' || 
          (statusFilter === 'success' && status.includes('成功')) ||
          (statusFilter === 'failed' && status.includes('失败'));
        
        let osMatch = osFilter === 'all' || os.includes(osFilter);
        let archMatch = archFilter === 'all' || arch.includes(archFilter);
        
        if (statusMatch && osMatch && archMatch) {
          row.style.display = '';
        } else {
          row.style.display = 'none';
        }
        
        // 总是隐藏详情行，除非被展开
        row.nextElementSibling.style.display = 'none';
      });
    }
    
    // 页面加载完成后初始化
    document.addEventListener('DOMContentLoaded', function() {
      // 初始隐藏所有详情行
      document.querySelectorAll('.details-row').forEach(row => {
        row.style.display = 'none';
      });
    });
  </script>
</body>
</html>