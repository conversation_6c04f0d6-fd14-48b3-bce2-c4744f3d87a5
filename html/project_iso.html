<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>制作 ISO - 项目 超融合 </title>
  <style>
    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }
    .header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
    }
    .back-btn {
      padding: 8px 15px;
      background-color: #2196F3;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
    }
    .form-container {
      background-color: #f9f9f9;
      padding: 20px;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }
    .form-group {
      margin-bottom: 15px;
      display: flex;
      flex-direction: column;
    }
    .form-row {
      display: flex;
      gap: 20px;
      margin-bottom: 15px;
    }
    .form-row .form-group {
      flex: 1;
    }
    .form-group label {
      margin-bottom: 5px;
      font-weight: bold;
    }
    .form-group input,
    .form-group select,
    .form-group textarea {
      padding: 10px;
      border: 1px solid #ddd;
      border-radius: 4px;
      font-size: 14px;
    }
    .checkbox-group {
      margin-bottom: 15px;
    }
    .checkbox-group label {
      font-weight: bold;
      margin-bottom: 8px;
      display: block;
    }
    .checkbox-item {
      margin-bottom: 8px;
      display: flex;
      align-items: center;
    }
    .modules-grid {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: 15px;
      margin-top: 15px;
      margin-bottom: 15px;
    }
    .modules-grid .checkbox-item {
      background-color: #f0f4ff;
      padding: 10px;
      border-radius: 5px;
      margin-bottom: 0;
      transition: background-color 0.2s;
    }
    .modules-grid .checkbox-item:hover {
      background-color: #e0e8ff;
    }
    .modules-grid .checkbox-item input[type="checkbox"] {
      margin-right: 8px;
    }
    .modules-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 15px;
    }
    .select-all-btn {
      background-color: #3f51b5;
      color: white;
      border: none;
      border-radius: 4px;
      padding: 5px 12px;
      cursor: pointer;
      font-size: 14px;
      transition: background-color 0.2s;
    }
    .select-all-btn:hover {
      background-color: #303f9f;
    }
    .module-counter {
      margin-right: 15px;
      font-size: 14px;
      color: #555;
    }
    .submit-btn {
      background-color: #4CAF50;
      color: white;
      padding: 12px 20px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      font-size: 16px;
      font-weight: bold;
      transition: background-color 0.3s;
    }
    .submit-btn:hover {
      background-color: #45a049;
    }
    .section-title {
      border-bottom: 2px solid #3f51b5;
      padding-bottom: 5px;
      margin-top: 20px;
      margin-bottom: 15px;
      color: #3f51b5;
    }
    /* 新增美化样式 */
    .action-link {
      color: #2196F3;
      text-decoration: none;
      margin: 0 5px;
    }
    table {
      width: 100%;
      border-collapse: collapse;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
      border-radius: 5px;
      overflow: hidden;
    }
    th {
      background-color: #3f51b5;
      color: white;
      padding: 12px 15px;
      text-align: left;
      font-weight: bold;
      font-size: 14px;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      border-bottom: 2px solid #ddd;
    }
    td {
      padding: 10px 15px;
      text-align: left;
      border-bottom: 1px solid #ddd;
    }
    tr:hover {
      background-color: #f5f5f5;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>📀 制作 ISO 镜像 - 超融合3.2</h1>
      <div>
        <button onclick="location.href='/html/project.html'" class="back-btn">返回项目列表</button>
      </div>
    </div>

    <div class="form-container">
      <form action="/iso/build" method="post">
        <!-- 项目信息 -->
        <input type="hidden" name="project_name" value="my-app" />
        
        <h2 class="section-title">基本信息</h2>
        <div class="form-row">
          <div class="form-group">
            <label for="project_display">项目名称：</label>
            <input type="text" id="project_display" name="project_display" value="超融合3.2" disabled />
          </div>
          <div class="form-group">
            <label for="description">描述：</label>
            <input type="text" id="description" name="description" value="云平台 openstack" />
          </div>
        </div>

        <h2 class="section-title">系统配置</h2>
        <div class="form-row">
          <div class="form-group">
            <label for="os">操作系统：</label>
            <select id="os" name="os">
              <option value="Anolis">Anolis</option>
              <option value="Kylin">Kylin</option>
            </select>
          </div>
          <div class="form-group">
            <label for="arch">CPU架构：</label>
            <select id="arch" name="arch">
              <option value="x86">x86</option>
              <option value="ARM">ARM</option>
            </select>
          </div>
          <div class="form-group">
            <label for="base_image">基础系统镜像：</label>
            <select id="base_image" name="base_image">
              <option value="anolis-8.8-arm.iso">anolis-8.8-arm.iso</option>
              <option value="kylin-v10-x86.iso">kylin-v10-x86.iso</option>
            </select>
          </div>
        </div>

        <h2 class="section-title">仓库配置</h2>
        <div class="form-row">
          <div class="form-group">
            <label for="rpm_repo">rpm 仓库地址：</label>
            <input type="url" id="rpm_repo" name="rpm_repo" placeholder="http://rpm.repo/xxx" required />
          </div>
          <div class="form-group">
            <label for="docker_repo">docker 仓库地址：</label>
            <input type="url" id="docker_repo" name="docker_repo" placeholder="http://docker.repo/xxx" required />
          </div>
        </div>
        <div class="form-row">
          <div class="form-group">
            <label for="pypi_repo">pypi 仓库地址：</label>
            <input type="url" id="pypi_repo" name="pypi_repo" placeholder="http://pypi.repo/xxx" required />
          </div>
        </div>

        <h2 class="section-title">代码配置</h2>
        <div class="form-row">
          <div class="form-group">
            <label for="branch">打包代码分支：</label>
            <input type="text" id="branch" name="branch" placeholder="如: arm 或 master" required />
          </div>
          <div class="form-group">
            <label for="commit">打包代码 Commit号：</label>
            <input type="text" id="commit" name="commit" placeholder="abcdef123456..." required />
          </div>
        </div>

        <h2 class="section-title">模块选择</h2>
        <div class="checkbox-group">
          <div class="modules-header">
            <label>选择包含的模块：</label>
            <div class="select-all-container">
              <span id="module-counter" class="module-counter">已选择 2 个模块</span>
              <button type="button" class="select-all-btn" onclick="toggleAllModules()">全选/全不选</button>
            </div>
          </div>
          <div class="modules-grid">
            <div class="checkbox-item">
              <input type="checkbox" id="module_frontend" name="modules" value="frontend" checked> 
              <label for="module_frontend">frontend</label>
            </div>
            <div class="checkbox-item">
              <input type="checkbox" id="module_api" name="modules" value="api" checked> 
              <label for="module_api">api</label>
            </div>
            <div class="checkbox-item">
              <input type="checkbox" id="module_scheduler" name="modules" value="scheduler"> 
              <label for="module_scheduler">scheduler</label>
            </div>
            <div class="checkbox-item">
              <input type="checkbox" id="module_database" name="modules" value="database"> 
              <label for="module_database">database</label>
            </div>
            <div class="checkbox-item">
              <input type="checkbox" id="module_monitoring" name="modules" value="monitoring"> 
              <label for="module_monitoring">monitoring</label>
            </div>
            <div class="checkbox-item">
              <input type="checkbox" id="module_authentication" name="modules" value="authentication"> 
              <label for="module_authentication">authentication</label>
            </div>
            <div class="checkbox-item">
              <input type="checkbox" id="module_networking" name="modules" value="networking"> 
              <label for="module_networking">networking</label>
            </div>
            <div class="checkbox-item">
              <input type="checkbox" id="module_storage" name="modules" value="storage"> 
              <label for="module_storage">storage</label>
            </div>
            <div class="checkbox-item">
              <input type="checkbox" id="module_compute" name="modules" value="compute"> 
              <label for="module_compute">compute</label>
            </div>
          </div>
        </div>

        <h2 class="section-title">发布配置</h2>
        <div class="form-group">
          <label for="iso_version">目标 ISO 版本号：</label>
          <input type="text" id="iso_version" name="iso_version" placeholder="如: my-app-v3.2.iso" required />
        </div>

        <div class="form-group">
          <label for="build_notes">构建说明：</label>
          <textarea id="build_notes" name="build_notes" rows="4" placeholder="本次构建包含内容、适配平台、优化点..."></textarea>
        </div>

        <div style="margin-top: 20px; text-align: center;">
          <button type="submit" class="submit-btn">🚀 开始构建 ISO</button>
        </div>
      </form>
    </div>
  </div>
  
  <script>
    // 页面加载时初始化
    document.addEventListener('DOMContentLoaded', function() {
      updateModuleCounter();
      
      // 为所有模块复选框添加更改事件监听器
      const checkboxes = document.querySelectorAll('input[name="modules"]');
      checkboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateModuleCounter);
      });
    });
    
    // 全选/全不选功能
    function toggleAllModules() {
      const checkboxes = document.querySelectorAll('input[name="modules"]');
      const allChecked = [...checkboxes].every(checkbox => checkbox.checked);
      
      checkboxes.forEach(checkbox => {
        checkbox.checked = !allChecked;
      });
      
      updateModuleCounter();
    }
    
    // 更新模块计数器
    function updateModuleCounter() {
      const checkboxes = document.querySelectorAll('input[name="modules"]');
      const checkedCount = [...checkboxes].filter(checkbox => checkbox.checked).length;
      const totalCount = checkboxes.length;
      
      document.getElementById('module-counter').textContent = `已选择 ${checkedCount} 个模块 (共 ${totalCount} 个)`;
    }
  </script>
</body>
</html>